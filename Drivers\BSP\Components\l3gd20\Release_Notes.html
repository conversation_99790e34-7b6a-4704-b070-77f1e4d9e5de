<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Release Notes for L3GD20 Component Drivers</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <link rel="stylesheet" href="_htmresc/mini-st_2020.css" />
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
  <link rel="icon" type="image/x-icon" href="_htmresc/favicon.png" />
</head>
<body>
<div class="row">
<div class="col-sm-12 col-lg-4">
<center>
<h1 id="release-notes-for-l3gd20-component-drivers"><small>Release Notes for</small> <mark>L3GD20 Component Drivers</mark></h1>
<p>Copyright © 2015 STMicroelectronics<br />
</p>
<a href="https://www.st.com" class="logo"><img src="_htmresc/st_logo_2020.png" alt="ST logo" /></a>
</center>
<h1 id="purpose">Purpose</h1>
<p>This directory contains the board drivers to demonstrate the capabilities of the L3GD20 Component Drivers.</p>
</div>
<div class="col-sm-12 col-lg-8">
<h1 id="update-history">Update History</h1>
<div class="collapse">
<input type="checkbox" id="collapse-section6" checked aria-hidden="true"> <label for="collapse-section6" aria-hidden="true">V2.0.2 / 05-June-2023</label>
<div>
<h2 id="main-changes">Main Changes</h2>
<ul>
<li>Update Release_Notes.html to support new format</li>
<li>All source files: update disclaimer to add reference to the new license agreement</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section5" aria-hidden="true"> <label for="collapse-section5" aria-hidden="true">V2.0.1 / 03-April-2019</label>
<div>
<h2 id="main-changes-1">Main Changes</h2>
<ul>
<li>Update release notes format</li>
<li>Reformat the BSD 3-Clause license declaration in the files header (replace license terms by a web reference to OSI website where those terms lie)</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section4" aria-hidden="true"> <label for="collapse-section4" aria-hidden="true">V2.0.0 / 24-June-2015</label>
<div>
<h2 id="main-changes-2">Main Changes</h2>
<ul>
<li>l3gd20.h/.c:
<ul>
<li>Add gyroscope de-initialization function: L3GD20_DeInit()</li>
<li>Add gyroscope low power configuration function: L3GD20_LowPower()</li>
</ul></li>
</ul>
<p><strong><span class="underline"><span style="font-size: 10pt; font-family: Verdana; color: black;">NOTE</span></span></strong> This release must be used with BSP Common driver V4.0.0 or later</p>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section3" aria-hidden="true"> <label for="collapse-section3" aria-hidden="true">V1.1.1 / 27-November-2014</label>
<div>
<h2 id="main-changes-3">Main Changes</h2>
<ul>
<li>3gd20.h: change “” by “/” in the include path to fix compilation issue under Linux</li>
<li>Miscellaneous formatting and comments update</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section2" aria-hidden="true"> <label for="collapse-section2" aria-hidden="true">V1.1.0 / 10-June-2014</label>
<div>
<h2 id="main-changes-4">Main Changes</h2>
<ul>
<li>Update to support new revision of L3GD20 component having new device ID 0xD5 (new define added: I_AM_L3GD20_TR)</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section1" aria-hidden="true"> <label for="collapse-section1" aria-hidden="true">V1.0.0 / 18-Fabruary-2014</label>
<div>
<h2 id="main-changes-5">Main Changes</h2>
<ul>
<li>First official release of L3GD20 gyroscope</li>
</ul>
</div>
</div>
</div>
</div>
<footer class="sticky">
For complete documentation on <mark>STM32 Microcontrollers</mark> , visit: <a href="http://www.st.com/STM32">http://www.st.com/STM32</a>
</footer>
</body>
</html>
