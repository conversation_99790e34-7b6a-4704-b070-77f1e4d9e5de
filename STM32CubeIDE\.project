<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>FatFs_USBDisk</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.st.stm32cube.ide.mcu.MCUProjectNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeIdeServicesRevAev2ProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUNonUnderRootProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUCubeExampleProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCUSingleCpuProjectNature</nature>
		<nature>com.st.stm32cube.ide.mcu.MCURootProjectNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>Doc/readme.txt</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/readme.txt</locationURI>
		</link>
		<link>
			<name>Application/User/main.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Src/main.c</locationURI>
		</link>
		<link>
			<name>Application/User/stm32f4xx_it.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Src/stm32f4xx_it.c</locationURI>
		</link>
		<link>
			<name>Application/User/usbh_conf.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Src/usbh_conf.c</locationURI>
		</link>
		<link>
			<name>Drivers/CMSIS/system_stm32f4xx.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Src/system_stm32f4xx.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_cortex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_dcmi.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dcmi.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_dma.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_dma_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_flash.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_flash_ex.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_gpio.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_hcd.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_hcd.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_i2c.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_pwr.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_rcc.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_hal_spi.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c</locationURI>
		</link>
		<link>
			<name>Drivers/STM32F4xx_HAL_Drivers/stm32f4xx_ll_usb.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_usb.c</locationURI>
		</link>
		<link>
			<name>Drivers/BSP/STM32F401_DISCO/stm32f401_discovery.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Drivers/BSP/STM32F401-Discovery/stm32f401_discovery.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FatFs/Core/diskio.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/diskio.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FatFs/Core/ff.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/ff.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FatFs/Core/ff_gen_drv.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/ff_gen_drv.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FatFs/Drivers/usbh_diskio_dma.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Src/usbh_diskio_dma.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FatFs/Options/syscall.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/option/syscall.c</locationURI>
		</link>
		<link>
			<name>Middlewares/FatFs/Options/unicode.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/Third_Party/FatFs/src/option/unicode.c</locationURI>
		</link>
		<link>
			<name>Middlewares/STM32_USBH_Library/Core/usbh_core.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_core.c</locationURI>
		</link>
		<link>
			<name>Middlewares/STM32_USBH_Library/Core/usbh_ctlreq.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ctlreq.c</locationURI>
		</link>
		<link>
			<name>Middlewares/STM32_USBH_Library/Core/usbh_ioreq.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ioreq.c</locationURI>
		</link>
		<link>
			<name>Middlewares/STM32_USBH_Library/Core/usbh_pipes.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_pipes.c</locationURI>
		</link>
		<link>
			<name>Middlewares/STM32_USBH_Library/Class/MSC/usbh_msc.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc.c</locationURI>
		</link>
		<link>
			<name>Middlewares/STM32_USBH_Library/Class/MSC/usbh_msc_bot.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_bot.c</locationURI>
		</link>
		<link>
			<name>Middlewares/STM32_USBH_Library/Class/MSC/usbh_msc_scsi.c</name>
			<type>1</type>
			<locationURI>PARENT-1-PROJECT_LOC/Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_scsi.c</locationURI>
		</link>
	</linkedResources>
</projectDescription>
