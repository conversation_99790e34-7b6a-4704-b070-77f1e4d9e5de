/**
  ******************************************************************************
  * @file    debug_config.h
  * <AUTHOR>
  * @brief   Debug configuration header file
  ******************************************************************************
  */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __DEBUG_CONFIG_H
#define __DEBUG_CONFIG_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include <stdio.h>
#include <stdint.h>

/* Debug Configuration -------------------------------------------------------*/

/* Enable/Disable debug modules */
#define DEBUG_ENABLED           1       /* Master debug enable/disable */
#define USB_DEBUG_ENABLED       1       /* USB Host debug messages */
#define MAIN_DEBUG_ENABLED      1       /* Main application debug messages */
#define FATFS_DEBUG_ENABLED     1       /* FatFs debug messages */

/* Debug output configuration */
#define DEBUG_USE_UART          0       /* Set to 1 if UART is available for debug output */
#define DEBUG_USE_SWO           0       /* Set to 1 if SWO is available for debug output */
#define DEBUG_USE_SEMIHOSTING   0       /* Set to 1 if semihosting is available */

/* Debug macros --------------------------------------------------------------*/
#if DEBUG_ENABLED

  /* USB Debug macros */
  #if USB_DEBUG_ENABLED
    #define USB_DEBUG_PRINT(fmt, ...) printf("[USB_DEBUG] " fmt "\r\n", ##__VA_ARGS__)
  #else
    #define USB_DEBUG_PRINT(fmt, ...)
  #endif

  /* Main Debug macros */
  #if MAIN_DEBUG_ENABLED
    #define MAIN_DEBUG_PRINT(fmt, ...) printf("[MAIN_DEBUG] " fmt "\r\n", ##__VA_ARGS__)
  #else
    #define MAIN_DEBUG_PRINT(fmt, ...)
  #endif

  /* FatFs Debug macros */
  #if FATFS_DEBUG_ENABLED
    #define FATFS_DEBUG_PRINT(fmt, ...) printf("[FATFS_DEBUG] " fmt "\r\n", ##__VA_ARGS__)
  #else
    #define FATFS_DEBUG_PRINT(fmt, ...)
  #endif

  /* General debug macro */
  #define DEBUG_PRINT(fmt, ...) printf("[DEBUG] " fmt "\r\n", ##__VA_ARGS__)

#else
  /* All debug disabled */
  #define USB_DEBUG_PRINT(fmt, ...)
  #define MAIN_DEBUG_PRINT(fmt, ...)
  #define FATFS_DEBUG_PRINT(fmt, ...)
  #define DEBUG_PRINT(fmt, ...)
#endif

/* Debug utility functions ---------------------------------------------------*/
#if DEBUG_ENABLED

/**
  * @brief  Print memory usage information
  * @param  None
  * @retval None
  */
void Debug_PrintMemoryInfo(void);

/**
  * @brief  Print current timestamp
  * @param  None
  * @retval None
  */
void Debug_PrintTimestamp(void);

/**
  * @brief  Print hex dump of data
  * @param  data: Pointer to data
  * @param  length: Length of data
  * @retval None
  */
void Debug_PrintHexDump(const uint8_t* data, uint32_t length);

/**
  * @brief  Print system reset reason
  * @param  None
  * @retval None
  */
void Debug_PrintResetReason(void);

#else
  #define Debug_PrintMemoryInfo()
  #define Debug_PrintTimestamp()
  #define Debug_PrintHexDump(data, length)
  #define Debug_PrintResetReason()
#endif

/* Debug error codes ---------------------------------------------------------*/
typedef enum {
  DEBUG_OK = 0,
  DEBUG_ERROR,
  DEBUG_TIMEOUT,
  DEBUG_INVALID_PARAM
} Debug_StatusTypeDef;

/* Debug statistics ----------------------------------------------------------*/
typedef struct {
  uint32_t usb_connect_count;
  uint32_t usb_disconnect_count;
  uint32_t usb_error_count;
  uint32_t fatfs_mount_count;
  uint32_t fatfs_error_count;
  uint32_t file_write_count;
  uint32_t file_read_count;
  uint32_t total_bytes_written;
  uint32_t total_bytes_read;
} Debug_StatsTypeDef;

extern Debug_StatsTypeDef debug_stats;

/* Debug function prototypes -------------------------------------------------*/
#if DEBUG_ENABLED
void Debug_Init(void);
void Debug_UpdateStats(void);
void Debug_PrintStats(void);
void Debug_ResetStats(void);
#else
  #define Debug_Init()
  #define Debug_UpdateStats()
  #define Debug_PrintStats()
  #define Debug_ResetStats()
#endif

#ifdef __cplusplus
}
#endif

#endif /* __DEBUG_CONFIG_H */
