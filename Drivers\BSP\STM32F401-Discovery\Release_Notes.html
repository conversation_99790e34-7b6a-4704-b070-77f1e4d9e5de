<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" lang="en" xml:lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="generator" content="pandoc" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes" />
  <title>Release Notes for STM32F401-Discovery Board Drivers</title>
  <style type="text/css">
      code{white-space: pre-wrap;}
      span.smallcaps{font-variant: small-caps;}
      span.underline{text-decoration: underline;}
      div.column{display: inline-block; vertical-align: top; width: 50%;}
  </style>
  <link rel="stylesheet" href="_htmresc/mini-st_2020.css" />
  <!--[if lt IE 9]>
    <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv-printshiv.min.js"></script>
  <![endif]-->
  <link rel="icon" type="image/x-icon" href="_htmresc/favicon.png" />
</head>
<body>
<div class="row">
<div class="col-sm-12 col-lg-4">
<center>
<h1 id="release-notes-for">Release Notes for</h1>
<h1 id="stm32f401-discovery-board-drivers"><mark>STM32F401-Discovery Board Drivers</mark></h1>
<p>Copyright © 2017 STMicroelectronics<br />
</p>
<a href="https://www.st.com" class="logo"><img src="_htmresc/st_logo_2020.png" alt="ST logo" /></a>
</center>
<h1 id="purpose">Purpose</h1>
<p>This directory contains the board drivers to demonstrate the capabilities of the STM32F401-Discovery Board Drivers.</p>
</div>
<div class="col-sm-12 col-lg-8">
<h1 id="update-history">Update History</h1>
<div class="collapse">
<input type="checkbox" id="collapse-section15" checked aria-hidden="true"> <label for="collapse-section15" aria-hidden="true">V2.2.5 / 22-September-2023</label>
<div>
<h2 id="main-changes">Main Changes</h2>
<ul>
<li>Update Release_Notes.html to support new format</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section12" aria-hidden="true"> <label for="collapse-section12" aria-hidden="true">V2.2.4 / 11-Fabruary-2022</label>
<div>
<h2 id="main-changes-1">Main Changes</h2>
<ul>
<li>All source files: update disclaimer to add reference to the new license agreement</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section11" aria-hidden="true"> <label for="collapse-section11" aria-hidden="true">V2.2.3 / 21-September-2017</label>
<div>
<h2 id="main-changes-2">Main Changes</h2>
<ul>
<li>Remove date &amp; version</li>
<li>Add general description of BSP drivers</li>
<li>stm32f401_discovery_audio.c/.h:
<ul>
<li>Aligned with PDM library v3.0.0</li>
</ul></li>
<li>Note
<ul>
<li>This version must be used with v3.0.0 of PDM library</li>
</ul></li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section10" aria-hidden="true"> <label for="collapse-section10" aria-hidden="true">V2.2.2 / 27-January-2017</label>
<div>
<h2 id="main-changes-3">Main Changes</h2>
<ul>
<li>Replace __PPP_CLK_ENABLE/DISABLE with __HAL_RCC_PPP_ENABLE/DISABLE</li>
<li>Add BSP_AUDIO_OUT_ClockConfig, BSP_AUDIO_OUT_MspInit and BSP_AUDIO_OUT_MspDeInit as weak functions</li>
<li>Add BSP_AUDIO_IN_ClockConfig, BSP_AUDIO_IN_MspInit and BSP_AUDIO_IN_MspDeInit as weak functions</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section9" aria-hidden="true"> <label for="collapse-section9" aria-hidden="true">V2.2.1 / 12-January-2016</label>
<div>
<h2 id="main-changes-4">Main Changes</h2>
<ul>
<li>General updates to fix doxygen errors</li>
<li>Add STM32F401-Discovery_BSP_User_Manual.chm file</li>
<li>stm32f401_discovery_audio.c
<ul>
<li>BSP_AUDIO_OUT_Init() update to configure correctly the PLL I2S parameters</li>
</ul></li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section8" aria-hidden="true"> <label for="collapse-section8" aria-hidden="true">V2.2.0 / 14-August-2015</label>
<div>
<h2 id="main-changes-5">Main Changes</h2>
<ul>
<li>stm32f401_discovery.c
<ul>
<li>Add AUDIO_IO_DeInit() function to align with BSP Components Common drivers V4.0.0</li>
</ul></li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section7" aria-hidden="true"> <label for="collapse-section7" aria-hidden="true">V2.1.3 / 26-June-2014</label>
<div>
<h2 id="main-changes-6">Main Changes</h2>
<ul>
<li>stm32f401_discovery.c/.h
<ul>
<li>Align to STM32F4xx HAL Driver V1.3.0 for __HAL_RCC_PPP_CLK_ENABLE() .</li>
</ul></li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section6" aria-hidden="true"> <label for="collapse-section6" aria-hidden="true">V2.1.2 / 10-December-2014</label>
<div>
<h2 id="main-changes-7">Main Changes</h2>
<ul>
<li>stm32f401_discovery_accelerometer.h, stm32f401_discovery_audio.h and stm32f401_discovery_gyroscope.h:
<ul>
<li>Change “" by”/" in the include path to fix compilation issue under Linux</li>
</ul></li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section5" aria-hidden="true"> <label for="collapse-section5" aria-hidden="true">V2.1.1 / 26-June-2014</label>
<div>
<h2 id="main-changes-8">Main Changes</h2>
<ul>
<li>stm32f401_discovery_audio.c/.h
<ul>
<li>BSP_AUDIO_OUT_Play(): uses the buffer size in byte instead of half-word</li>
<li>BSP_AUDIO_IN_PDMToPCM(): duplicates samples to make stereo audio stream (since a single microphone in mounted on STM32F401-Discovery)</li>
<li>I2S2_Init(): configures the I2S to clock the microphone at 1.024 MHz as required instead of 2.048MHz</li>
</ul></li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section4" aria-hidden="true"> <label for="collapse-section4" aria-hidden="true">V2.1.0 / 19-June-2014</label>
<div>
<h2 id="main-changes-9">Main Changes</h2>
<ul>
<li>stm32f401_discovery.c/.h
<ul>
<li>Enhance BSP_PB_Init() function by removing the call of __SYSCFG_CLK_ENABLE() already enabled in the HAL_GPIO_Init()</li>
</ul></li>
<li>stm32f401_discovery_audio.c/.h
<ul>
<li>Add note that only the audio stereo format is supported</li>
</ul></li>
<li>stm32f401_discovery_gyroscope.c/.h
<ul>
<li>Update BSP_GYRO_Init() to support new L3GD20 device ID (I_AM_L3GD20_TR)</li>
</ul></li>
<li>Comments clean up and typo corrections</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section3" aria-hidden="true"> <label for="collapse-section3" aria-hidden="true">V2.0.0 / 18-Fabruary-2014</label>
<div>
<h2 id="main-changes-10">Main Changes</h2>
<ul>
<li>Major update based on STM32Cube specification: drivers architecture and APIs modified vs. V1.0.1, and thus the 2 versions are not compatible.</li>
<li>This version has to be used only with STM32CubeF4 based development</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section2" aria-hidden="true"> <label for="collapse-section2" aria-hidden="true">V1.0.1 / 14-November-2013</label>
<div>
<h2 id="main-changes-11">Main Changes</h2>
<ul>
<li>stm32f429i_discovery_l3gd20.c : Set SPI baudrate to 5.25 MHz to fit Gyroscope timing characteristics (Gyroscope l3gd20 SPI interface max baud rate is 10MHz for write/read)</li>
</ul>
</div>
</div>
<div class="collapse">
<input type="checkbox" id="collapse-section1" aria-hidden="true"> <label for="collapse-section1" aria-hidden="true">V1.0.0 / 11-September-2013</label>
<div>
<h2 id="main-changes-12">Main Changes</h2>
<ul>
<li>First official version of the STM32F401-Discovery Board Drivers</li>
</ul>
</div>
</div>
</div>
</div>
<footer class="sticky">
For complete documentation on <mark>STM32 Microcontrollers</mark> , visit: <a href="http://www.st.com/STM32">http://www.st.com/STM32</a>
</footer>
</body>
</html>
