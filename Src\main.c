/**
  ******************************************************************************
  * @file    FatFs/FatFs_USBDisk/Src/main.c 
  * <AUTHOR> Application Team
  * @brief   Main program body
  *          This sample code shows how to use FatFs with USB disk drive.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>

/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
FATFS USBDISKFatFs;           /* File system object for USB disk logical drive */
FIL MyFile;                   /* File object */
char USBDISKPath[4];          /* USB Host logical drive path */
USBH_HandleTypeDef hUSB_Host; /* USB Host handle */

typedef enum {
  APPLICATION_IDLE = 0,
  APPLICATION_START,
  APPLICATION_RUNNING,
}MSC_ApplicationTypeDef;

MSC_ApplicationTypeDef Appli_state = APPLICATION_IDLE;

/* Debug variables */
static uint32_t main_loop_count = 0;

/* Printf redirection for debugging (if UART is available) */
#ifdef __GNUC__
  /* With GCC, small printf (option LD Linker->Libraries->Small printf
     set to 'Yes') calls __io_putchar() */
  #define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
  #define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif /* __GNUC__ */

/**
  * @brief  Retargets the C library printf function to the USART.
  * @param  None
  * @retval None
  */
PUTCHAR_PROTOTYPE
{
  /* Place your implementation of fputc here */
  /* e.g. write a character to the USART2 and Loop until the end of transmission */
  /* HAL_UART_Transmit(&huart2, (uint8_t *)&ch, 1, 0xFFFF); */

  /* For now, just return the character (no actual output) */
  /* You can implement UART output here if needed */
  return ch;
}

/* Private function prototypes -----------------------------------------------*/
static void SystemClock_Config(void);
static void Error_Handler(void);
static void USBH_UserProcess(USBH_HandleTypeDef *phost, uint8_t id);
static void MSC_Application(void);
static void Debug_PrintSystemInfo(void);
static void Debug_PrintUSBHostState(void);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
  /* STM32F4xx HAL library initialization:
       - Configure the Flash prefetch, instruction and Data caches
       - Configure the Systick to generate an interrupt each 1 msec
       - Set NVIC Group Priority to 4
       - Global MSP (MCU Support Package) initialization
     */
  HAL_Init();

  /* Initialize debug system */
  Debug_Init();

  MAIN_DEBUG_PRINT("=== STM32F401 USB Host FatFs Demo Started ===");
  MAIN_DEBUG_PRINT("HAL_Init: System initialization completed");

  /* Configure LED4 and LED5 */
  BSP_LED_Init(LED4);
  BSP_LED_Init(LED5);
  MAIN_DEBUG_PRINT("LED_Init: LED4 and LED5 initialized");

  /* Configure the system clock to 84 MHz */
  SystemClock_Config();
  MAIN_DEBUG_PRINT("SystemClock_Config: System clock configured to 84MHz");

  /* Print system information */
  Debug_PrintSystemInfo();
  Debug_PrintMemoryInfo();

  /*##-1- Link the USB Host disk I/O driver ##################################*/
  MAIN_DEBUG_PRINT("Linking USB Host disk I/O driver...");
  if(FATFS_LinkDriver(&USBH_Driver, USBDISKPath) == 0)
  {
    MAIN_DEBUG_PRINT("FATFS_LinkDriver: Successfully linked USB Host driver");
    MAIN_DEBUG_PRINT("USB Disk Path: %s", USBDISKPath);

    /*##-2- Init Host Library ################################################*/
    MAIN_DEBUG_PRINT("Initializing USB Host Library...");
    USBH_Init(&hUSB_Host, USBH_UserProcess, 0);
    MAIN_DEBUG_PRINT("USBH_Init: USB Host Library initialized");

    /*##-3- Add Supported Class ##############################################*/
    MAIN_DEBUG_PRINT("Registering Mass Storage Class...");
    USBH_RegisterClass(&hUSB_Host, USBH_MSC_CLASS);
    MAIN_DEBUG_PRINT("USBH_RegisterClass: MSC class registered");

    /*##-4- Start Host Process ###############################################*/
    MAIN_DEBUG_PRINT("Starting USB Host Process...");
    USBH_Start(&hUSB_Host);
    MAIN_DEBUG_PRINT("USBH_Start: USB Host started, waiting for device connection...");

    /*##-5- Run Application (Blocking mode) ##################################*/
    MAIN_DEBUG_PRINT("Entering main application loop");
    while (1)
    {
      main_loop_count++;

      /* USB Host Background task */
      USBH_Process(&hUSB_Host);

      /* Print loop status every 100000 iterations */
      if(main_loop_count % 100000 == 0)
      {
        MAIN_DEBUG_PRINT("Main loop running... (Count: %" PRIu32 ", State: %d)", main_loop_count, Appli_state);
        Debug_PrintUSBHostState();
      }

      /* Mass Storage Application State Machine */
      switch(Appli_state)
      {
      case APPLICATION_START:
        MAIN_DEBUG_PRINT("APPLICATION_START: Starting MSC application");
        MSC_Application();
        Appli_state = APPLICATION_IDLE;
        MAIN_DEBUG_PRINT("APPLICATION_START: MSC application completed, returning to IDLE");
        break;

      case APPLICATION_IDLE:
      default:
        break;
      }
    }
  }
  else
  {
    MAIN_DEBUG_PRINT("ERROR: Failed to link USB Host disk I/O driver");
    BSP_LED_On(LED5);
  }

  /* TrueStudio compilation error correction */
  MAIN_DEBUG_PRINT("ERROR: Reached end of main function - should not happen");
  while (1)
  {
  }
}

/**
  * @brief  Main routine for Mass Storage Class
  * @param  None
  * @retval None
  */
static void MSC_Application(void)
{
  FRESULT res;                                          /* FatFs function common result code */
  uint32_t byteswritten, bytesread;                     /* File write/read counts */
  uint8_t wtext[] = "This is STM32 working with FatFs"; /* File write buffer */
  uint8_t rtext[100];                                   /* File read buffer */

  MAIN_DEBUG_PRINT("=== MSC Application Started ===");

  /* Register the file system object to the FatFs module */
  MAIN_DEBUG_PRINT("MSC_APP: Mounting file system on path: %s", USBDISKPath);
  res = f_mount(&USBDISKFatFs, (TCHAR const*)USBDISKPath, 0);
  if(res != FR_OK)
  {
    MAIN_DEBUG_PRINT("MSC_APP: ERROR - f_mount failed with code: %d", res);
    Error_Handler();
  }
  else
  {
    MAIN_DEBUG_PRINT("MSC_APP: File system mounted successfully");

    /* Create and Open a new text file object with write access */
    MAIN_DEBUG_PRINT("MSC_APP: Creating/Opening file 'STM32.TXT' for write...");
    res = f_open(&MyFile, "STM32.TXT", FA_CREATE_ALWAYS | FA_WRITE);
    if(res != FR_OK)
    {
      MAIN_DEBUG_PRINT("MSC_APP: ERROR - f_open for write failed with code: %d", res);
      Error_Handler();
    }
    else
    {
      MAIN_DEBUG_PRINT("MSC_APP: File opened successfully for writing");

      /* Write data to the text file */
      MAIN_DEBUG_PRINT("MSC_APP: Writing data: '%s' (%d bytes)", wtext, sizeof(wtext));
      res = f_write(&MyFile, wtext, sizeof(wtext), (void *)&byteswritten);

      if((byteswritten == 0) || (res != FR_OK))
      {
        MAIN_DEBUG_PRINT("MSC_APP: ERROR - f_write failed. Code: %d, Bytes written: %" PRIu32, res, byteswritten);
        Error_Handler();
      }
      else
      {
        MAIN_DEBUG_PRINT("MSC_APP: Write successful - %lu bytes written", byteswritten);

        /* Close the open text file */
        f_close(&MyFile);
        MAIN_DEBUG_PRINT("MSC_APP: File closed after writing");

        /* Open the text file object with read access */
        MAIN_DEBUG_PRINT("MSC_APP: Opening file 'STM32.TXT' for read...");
        res = f_open(&MyFile, "STM32.TXT", FA_READ);
        if(res != FR_OK)
        {
          MAIN_DEBUG_PRINT("MSC_APP: ERROR - f_open for read failed with code: %d", res);
          Error_Handler();
        }
        else
        {
          MAIN_DEBUG_PRINT("MSC_APP: File opened successfully for reading");

          /* Read data from the text file */
          MAIN_DEBUG_PRINT("MSC_APP: Reading data from file...");
          res = f_read(&MyFile, rtext, sizeof(rtext), (void *)&bytesread);

          if((bytesread == 0) || (res != FR_OK))
          {
            MAIN_DEBUG_PRINT("MSC_APP: ERROR - f_read failed. Code: %d, Bytes read: %lu", res, bytesread);
            Error_Handler();
          }
          else
          {
            MAIN_DEBUG_PRINT("MSC_APP: Read successful - %lu bytes read", bytesread);
            rtext[bytesread] = '\0'; // Null terminate for safe printing
            MAIN_DEBUG_PRINT("MSC_APP: Read data: '%s'", rtext);

            /* Close the open text file */
            f_close(&MyFile);
            MAIN_DEBUG_PRINT("MSC_APP: File closed after reading");

            /* Compare read data with the expected data */
            if((bytesread != byteswritten))
            {
              MAIN_DEBUG_PRINT("MSC_APP: ERROR - Data mismatch! Written: %lu bytes, Read: %lu bytes", byteswritten, bytesread);
              Error_Handler();
            }
            else
            {
              MAIN_DEBUG_PRINT("MSC_APP: SUCCESS - Data verification passed!");
              MAIN_DEBUG_PRINT("MSC_APP: Turning on LED4 to indicate success");
              BSP_LED_On(LED4);
            }
          }
        }
      }
    }
  }

  /* Unlink the USB disk I/O driver */
  MAIN_DEBUG_PRINT("MSC_APP: Unlinking USB disk I/O driver");
  FATFS_UnLinkDriver(USBDISKPath);
  MAIN_DEBUG_PRINT("=== MSC Application Completed ===");
}

/**
  * @brief  User Process
  * @param  phost: Host handle
  * @param  id: Host Library user message ID
  * @retval None
  */
static void USBH_UserProcess(USBH_HandleTypeDef *phost, uint8_t id)
{
  switch(id)
  {
  case HOST_USER_SELECT_CONFIGURATION:
    MAIN_DEBUG_PRINT("USER_PROCESS: HOST_USER_SELECT_CONFIGURATION");
    break;

  case HOST_USER_DISCONNECTION:
    MAIN_DEBUG_PRINT("USER_PROCESS: HOST_USER_DISCONNECTION - Device disconnected");
    debug_stats.usb_disconnect_count++;
    Appli_state = APPLICATION_IDLE;
    BSP_LED_Off(LED4);
    BSP_LED_Off(LED5);
    f_mount(NULL, (TCHAR const*)"", 0);
    MAIN_DEBUG_PRINT("USER_PROCESS: File system unmounted, LEDs turned off");
    Debug_PrintStats();
    break;

  case HOST_USER_CLASS_ACTIVE:
    MAIN_DEBUG_PRINT("USER_PROCESS: HOST_USER_CLASS_ACTIVE - MSC class is active");
    debug_stats.usb_connect_count++;
    Appli_state = APPLICATION_START;
    MAIN_DEBUG_PRINT("USER_PROCESS: Application state set to START");
    break;

  default:
    MAIN_DEBUG_PRINT("USER_PROCESS: Unknown user process ID: %d", id);
    break;
  }
}

/**
  * @brief  System Clock Configuration
  *         The system Clock is configured as follow : 
  *            System Clock source            = PLL (HSE)
  *            SYSCLK(Hz)                     = 84000000
  *            HCLK(Hz)                       = 84000000
  *            AHB Prescaler                  = 1
  *            APB1 Prescaler                 = 2
  *            APB2 Prescaler                 = 1
  *            HSE Frequency(Hz)              = 8000000
  *            PLL_M                          = 8
  *            PLL_N                          = 336
  *            PLL_P                          = 4
  *            PLL_Q                          = 7
  *            VDD(V)                         = 3.3
  *            Main regulator output voltage  = Scale2 mode
  *            Flash Latency(WS)              = 2
  * @param  None
  * @retval None
  */
static void SystemClock_Config(void)
{
  RCC_ClkInitTypeDef RCC_ClkInitStruct;
  RCC_OscInitTypeDef RCC_OscInitStruct;
  
  /* Enable Power Control clock */
  __HAL_RCC_PWR_CLK_ENABLE();
  
  /* The voltage scaling allows optimizing the power consumption when the device is 
     clocked below the maximum system frequency, to update the voltage scaling value 
     regarding system frequency refer to product datasheet.  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE2);
  
  /* Enable HSE Oscillator and activate PLL with HSE as source */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 336;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV4;
  RCC_OscInitStruct.PLL.PLLQ = 7;
  HAL_RCC_OscConfig(&RCC_OscInitStruct);
 
  /* Select PLL as system clock source and configure the HCLK, PCLK1 and PCLK2 
     clocks dividers */
  RCC_ClkInitStruct.ClockType = (RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2);
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV2;  
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;  
  HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_2);
}

/**
  * @brief  This function is executed in case of error occurrence.
  * @param  None
  * @retval None
  */
static void Error_Handler(void)
{
  /* Turn LED5 on */
  MAIN_DEBUG_PRINT("*** ERROR_HANDLER: Critical error occurred! ***");
  MAIN_DEBUG_PRINT("ERROR_HANDLER: Turning on LED5 and entering infinite loop");
  BSP_LED_On(LED5);
  while(1)
  {
    // Stay here forever to indicate error
  }
}

/**
  * @brief  Print system information for debugging
  * @param  None
  * @retval None
  */
static void Debug_PrintSystemInfo(void)
{
  MAIN_DEBUG_PRINT("=== System Information ===");
  MAIN_DEBUG_PRINT("MCU: STM32F401RCT6");
  MAIN_DEBUG_PRINT("System Clock: %lu Hz", HAL_RCC_GetSysClockFreq());
  MAIN_DEBUG_PRINT("HCLK: %lu Hz", HAL_RCC_GetHCLKFreq());
  MAIN_DEBUG_PRINT("PCLK1: %lu Hz", HAL_RCC_GetPCLK1Freq());
  MAIN_DEBUG_PRINT("PCLK2: %lu Hz", HAL_RCC_GetPCLK2Freq());
  MAIN_DEBUG_PRINT("HAL Tick Frequency: %lu Hz", HAL_GetTickFreq());
  MAIN_DEBUG_PRINT("=========================");
}

/**
  * @brief  Print USB Host state information
  * @param  None
  * @retval None
  */
static void Debug_PrintUSBHostState(void)
{
  static uint32_t last_state = 0xFF;
  uint32_t current_state = hUSB_Host.gState;

  if(current_state != last_state)
  {
    const char* state_str;
    switch(current_state)
    {
      case HOST_IDLE:           state_str = "IDLE"; break;
      case HOST_DEV_WAIT_FOR_ATTACHMENT: state_str = "WAIT_FOR_ATTACHMENT"; break;
      case HOST_DEV_ATTACHED:   state_str = "ATTACHED"; break;
      case HOST_DEV_DISCONNECTED: state_str = "DISCONNECTED"; break;
      case HOST_DETECT_DEVICE_SPEED: state_str = "DETECT_SPEED"; break;
      case HOST_ENUMERATION:    state_str = "ENUMERATION"; break;
      case HOST_CLASS_REQUEST:  state_str = "CLASS_REQUEST"; break;
      case HOST_INPUT:          state_str = "INPUT"; break;
      case HOST_SET_CONFIGURATION: state_str = "SET_CONFIGURATION"; break;
      case HOST_CHECK_CLASS:    state_str = "CHECK_CLASS"; break;
      case HOST_CLASS:          state_str = "CLASS"; break;
      case HOST_SUSPENDED:      state_str = "SUSPENDED"; break;
      case HOST_ABORT_STATE:    state_str = "ABORT"; break;
      default:                  state_str = "UNKNOWN"; break;
    }
    MAIN_DEBUG_PRINT("USB Host State Changed: %s (%lu)", state_str, current_state);
    last_state = current_state;
  }
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t* file, uint32_t line)
{
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  MAIN_DEBUG_PRINT("ASSERT_FAILED: File %s, Line %lu", file, line);

  /* Infinite loop */
  while (1)
  {
  }
}
#endif
