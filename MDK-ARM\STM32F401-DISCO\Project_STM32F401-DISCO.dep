Dependencies for Project 'Project', Target 'STM32F401-DISCO': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (../readme.txt)(0x681F14B8)()
F (../Src/system_stm32f4xx.c)(0x681F14B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/system_stm32f4xx.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/BSP/STM32F401-Discovery/stm32f401_discovery.c)(0x681F147B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f401_discovery.o -MMD)
I (..\Drivers\BSP\STM32F401-Discovery\stm32f401_discovery.h)(0x681F147B)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Middlewares/Third_Party/FatFs/src/diskio.c)(0x681F147E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/diskio.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\inc\ffconf.h)(0x681F14B8)
F (../Middlewares/Third_Party/FatFs/src/ff.c)(0x681F147E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/ff.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\inc\ffconf.h)(0x681F14B8)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
F (../Middlewares/Third_Party/FatFs/src/ff_gen_drv.c)(0x681F147E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/ff_gen_drv.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\inc\ffconf.h)(0x681F14B8)
F (../Middlewares/Third_Party/FatFs/src/option/syscall.c)(0x681F147E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/syscall.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\option\..\ff.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\option\..\integer.h)(0x681F147E)
I (..\inc\ffconf.h)(0x681F14B8)
F (../Middlewares/Third_Party/FatFs/src/option/unicode.c)(0x681F147E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/unicode.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\option\..\ff.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\option\..\integer.h)(0x681F147E)
I (..\inc\ffconf.h)(0x681F14B8)
I (..\Middlewares\Third_Party\FatFs\src\option\ccsbcs.c)(0x681F147E)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_dma_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_gpio.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_usb.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_ll_usb.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_rcc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_flash.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_dma.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_flash_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dcmi.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_dcmi.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_cortex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_i2c.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_hcd.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_hcd.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_pwr.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_hal_spi.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_pipes.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/usbh_pipes.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_core.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/usbh_core.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ioreq.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/usbh_ioreq.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ctlreq.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/usbh_ctlreq.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
F (../Src/usbh_diskio_dma.c)(0x681F14B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/usbh_diskio_dma.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\inc\ffconf.h)(0x681F14B8)
I (..\inc\usbh_diskio_dma.h)(0x681F14B8)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
F (../Src/usbh_conf.c)(0x684AD0DB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/usbh_conf.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
F (../Src/main.c)(0x684AD22C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/main.o -MMD)
I (..\inc\main.h)(0x684AD1D6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Drivers\BSP\STM32F401-Discovery\stm32f401_discovery.h)(0x681F147B)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\inc\ffconf.h)(0x681F14B8)
I (..\inc\usbh_diskio_dma.h)(0x681F14B8)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
I (..\inc\debug_config.h)(0x684AD1CB)
F (../Src/stm32f4xx_it.c)(0x681F14B8)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/stm32f4xx_it.o -MMD)
I (..\inc\main.h)(0x684AD1D6)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Drivers\BSP\STM32F401-Discovery\stm32f401_discovery.h)(0x681F147B)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\inc\ffconf.h)(0x681F14B8)
I (..\inc\usbh_diskio_dma.h)(0x681F14B8)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
I (..\inc\debug_config.h)(0x684AD1CB)
I (..\inc\stm32f4xx_it.h)(0x681F14B8)
F (../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_bot.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/usbh_msc_bot.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/usbh_msc.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_scsi.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F401-Discovery -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F401-DISCO

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F401xC -D_RTE_ -DUSE_HAL_DRIVER -DSTM32F401xC -DUSE_STM32F401_DISCO

-o stm32f401-disco/usbh_msc_scsi.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\inc\usbh_conf.h)(0x681F14B8)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f401xc.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\inc\stm32f4xx_hal_conf.h)(0x681F14B8)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_spi.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
F (startup_stm32f401xc.s)(0x681F14B8)(--cpu Cortex-M4.fp.sp -g --pd "__MICROLIB SETA 1" --diag_suppress=A1950W

-I.\RTE\_STM32F401-DISCO

-ID:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include

--pd "__UVISION_VERSION SETA 542"

--pd "STM32F401xC SETA 1"

--pd "_RTE_ SETA 1"

--list startup_stm32f401xc.lst

--xref -o stm32f401-disco\startup_stm32f401xc.o

--depend stm32f401-disco\startup_stm32f401xc.d)
