/**
  ******************************************************************************
  * @file    debug_utils.c
  * <AUTHOR>
  * @brief   Debug utility functions implementation
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "debug_config.h"
#include "stm32f4xx_hal.h"
#include <string.h>

/* Private variables ---------------------------------------------------------*/
Debug_StatsTypeDef debug_stats = {0};

/* Private function prototypes -----------------------------------------------*/
#if DEBUG_ENABLED
static const char* Debug_GetResetReasonString(uint32_t reset_flags);
#endif

/* Public functions ----------------------------------------------------------*/

#if DEBUG_ENABLED

/**
  * @brief  Initialize debug system
  * @param  None
  * @retval None
  */
void Debug_Init(void)
{
  DEBUG_PRINT("Debug system initialized");
  Debug_ResetStats();
  Debug_PrintResetReason();
  Debug_PrintTimestamp();
}

/**
  * @brief  Update debug statistics
  * @param  None
  * @retval None
  */
void Debug_UpdateStats(void)
{
  // This function can be called periodically to update statistics
  // Implementation depends on specific requirements
}

/**
  * @brief  Print debug statistics
  * @param  None
  * @retval None
  */
void Debug_PrintStats(void)
{
  DEBUG_PRINT("=== Debug Statistics ===");
  DEBUG_PRINT("USB Connections: %lu", debug_stats.usb_connect_count);
  DEBUG_PRINT("USB Disconnections: %lu", debug_stats.usb_disconnect_count);
  DEBUG_PRINT("USB Errors: %lu", debug_stats.usb_error_count);
  DEBUG_PRINT("FatFs Mounts: %lu", debug_stats.fatfs_mount_count);
  DEBUG_PRINT("FatFs Errors: %lu", debug_stats.fatfs_error_count);
  DEBUG_PRINT("File Writes: %lu", debug_stats.file_write_count);
  DEBUG_PRINT("File Reads: %lu", debug_stats.file_read_count);
  DEBUG_PRINT("Total Bytes Written: %lu", debug_stats.total_bytes_written);
  DEBUG_PRINT("Total Bytes Read: %lu", debug_stats.total_bytes_read);
  DEBUG_PRINT("========================");
}

/**
  * @brief  Reset debug statistics
  * @param  None
  * @retval None
  */
void Debug_ResetStats(void)
{
  memset(&debug_stats, 0, sizeof(debug_stats));
  DEBUG_PRINT("Debug statistics reset");
}

/**
  * @brief  Print memory usage information
  * @param  None
  * @retval None
  */
void Debug_PrintMemoryInfo(void)
{
  extern uint32_t _estack;
  extern uint32_t _Min_Stack_Size;
  
  uint32_t stack_top = (uint32_t)&_estack;
  uint32_t current_sp;
  
  // Get current stack pointer
  __asm volatile ("mov %0, sp" : "=r" (current_sp));
  
  uint32_t stack_used = stack_top - current_sp;
  uint32_t min_stack_size = (uint32_t)&_Min_Stack_Size;
  
  DEBUG_PRINT("=== Memory Information ===");
  DEBUG_PRINT("Stack Top: 0x%08lX", stack_top);
  DEBUG_PRINT("Current SP: 0x%08lX", current_sp);
  DEBUG_PRINT("Stack Used: %lu bytes", stack_used);
  DEBUG_PRINT("Min Stack Size: %lu bytes", min_stack_size);
  DEBUG_PRINT("Stack Available: %lu bytes", current_sp - (stack_top - min_stack_size));
  DEBUG_PRINT("==========================");
}

/**
  * @brief  Print current timestamp
  * @param  None
  * @retval None
  */
void Debug_PrintTimestamp(void)
{
  uint32_t tick = HAL_GetTick();
  uint32_t seconds = tick / 1000;
  uint32_t milliseconds = tick % 1000;
  
  DEBUG_PRINT("Timestamp: %lu.%03lu seconds", seconds, milliseconds);
}

/**
  * @brief  Print hex dump of data
  * @param  data: Pointer to data
  * @param  length: Length of data
  * @retval None
  */
void Debug_PrintHexDump(const uint8_t* data, uint32_t length)
{
  if(data == NULL || length == 0)
  {
    DEBUG_PRINT("HexDump: Invalid parameters");
    return;
  }
  
  DEBUG_PRINT("=== Hex Dump (%lu bytes) ===", length);
  
  for(uint32_t i = 0; i < length; i += 16)
  {
    // Print address
    printf("%08lX: ", i);
    
    // Print hex values
    for(uint32_t j = 0; j < 16; j++)
    {
      if(i + j < length)
      {
        printf("%02X ", data[i + j]);
      }
      else
      {
        printf("   ");
      }
    }
    
    // Print ASCII representation
    printf(" |");
    for(uint32_t j = 0; j < 16 && (i + j) < length; j++)
    {
      uint8_t c = data[i + j];
      printf("%c", (c >= 32 && c <= 126) ? c : '.');
    }
    printf("|\r\n");
  }
  
  DEBUG_PRINT("========================");
}

/**
  * @brief  Print system reset reason
  * @param  None
  * @retval None
  */
void Debug_PrintResetReason(void)
{
  uint32_t reset_flags = RCC->CSR;
  const char* reset_reason = Debug_GetResetReasonString(reset_flags);
  
  DEBUG_PRINT("=== Reset Information ===");
  DEBUG_PRINT("Reset Flags: 0x%08lX", reset_flags);
  DEBUG_PRINT("Reset Reason: %s", reset_reason);
  DEBUG_PRINT("=========================");
  
  // Clear reset flags
  __HAL_RCC_CLEAR_RESET_FLAGS();
}

/**
  * @brief  Get reset reason string
  * @param  reset_flags: Reset flags from RCC->CSR
  * @retval Pointer to reset reason string
  */
static const char* Debug_GetResetReasonString(uint32_t reset_flags)
{
  if(reset_flags & RCC_CSR_LPWRRSTF)
    return "Low Power Reset";
  else if(reset_flags & RCC_CSR_WWDGRSTF)
    return "Window Watchdog Reset";
  else if(reset_flags & RCC_CSR_IWDGRSTF)
    return "Independent Watchdog Reset";
  else if(reset_flags & RCC_CSR_SFTRSTF)
    return "Software Reset";
  else if(reset_flags & RCC_CSR_PORRSTF)
    return "Power-On Reset";
  else if(reset_flags & RCC_CSR_PINRSTF)
    return "Pin Reset";
  else if(reset_flags & RCC_CSR_BORRSTF)
    return "Brown-Out Reset";
  else
    return "Unknown Reset";
}

#endif /* DEBUG_ENABLED */
