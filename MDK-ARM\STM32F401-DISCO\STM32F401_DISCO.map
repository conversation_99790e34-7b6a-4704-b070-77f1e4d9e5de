Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    stm32f401_discovery.o(.ARM.exidx.text.BSP_GetVersion) refers to stm32f401_discovery.o(.text.BSP_GetVersion) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_LED_Init) refers to stm32f401_discovery.o(.rodata.GPIO_PIN) for GPIO_PIN
    stm32f401_discovery.o(.text.BSP_LED_Init) refers to stm32f401_discovery.o(.data.GPIO_PORT) for GPIO_PORT
    stm32f401_discovery.o(.text.BSP_LED_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.BSP_LED_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Init) refers to stm32f401_discovery.o(.text.BSP_LED_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_LED_On) refers to stm32f401_discovery.o(.data.GPIO_PORT) for GPIO_PORT
    stm32f401_discovery.o(.text.BSP_LED_On) refers to stm32f401_discovery.o(.rodata.GPIO_PIN) for GPIO_PIN
    stm32f401_discovery.o(.text.BSP_LED_On) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_On) refers to stm32f401_discovery.o(.text.BSP_LED_On) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_LED_Off) refers to stm32f401_discovery.o(.data.GPIO_PORT) for GPIO_PORT
    stm32f401_discovery.o(.text.BSP_LED_Off) refers to stm32f401_discovery.o(.rodata.GPIO_PIN) for GPIO_PIN
    stm32f401_discovery.o(.text.BSP_LED_Off) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Off) refers to stm32f401_discovery.o(.text.BSP_LED_Off) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_LED_Toggle) refers to stm32f401_discovery.o(.data.GPIO_PORT) for GPIO_PORT
    stm32f401_discovery.o(.text.BSP_LED_Toggle) refers to stm32f401_discovery.o(.rodata.GPIO_PIN) for GPIO_PIN
    stm32f401_discovery.o(.text.BSP_LED_Toggle) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Toggle) refers to stm32f401_discovery.o(.text.BSP_LED_Toggle) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_PB_Init) refers to stm32f401_discovery.o(.data.BUTTON_PORT) for BUTTON_PORT
    stm32f401_discovery.o(.text.BSP_PB_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.BSP_PB_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f401_discovery.o(.text.BSP_PB_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f401_discovery.o(.ARM.exidx.text.BSP_PB_Init) refers to stm32f401_discovery.o(.text.BSP_PB_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_PB_GetState) refers to stm32f401_discovery.o(.data.BUTTON_PORT) for BUTTON_PORT
    stm32f401_discovery.o(.text.BSP_PB_GetState) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_PB_GetState) refers to stm32f401_discovery.o(.text.BSP_PB_GetState) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.GYRO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.GYRO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.text.GYRO_IO_Init) refers to stm32f401_discovery.o(.text.SPIx_Init) for SPIx_Init
    stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Init) refers to stm32f401_discovery.o(.text.GYRO_IO_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.SPIx_Init) refers to stm32f401_discovery.o(.bss.SpiHandle) for SpiHandle
    stm32f401_discovery.o(.text.SPIx_Init) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_GetState) for HAL_SPI_GetState
    stm32f401_discovery.o(.text.SPIx_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.SPIx_Init) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Init) for HAL_SPI_Init
    stm32f401_discovery.o(.ARM.exidx.text.SPIx_Init) refers to stm32f401_discovery.o(.text.SPIx_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f401_discovery.o(.data.SpixTimeout) for SpixTimeout
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f401_discovery.o(.bss.SpiHandle) for SpiHandle
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit) for HAL_SPI_DeInit
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f401_discovery.o(.text.SPIx_Init) for SPIx_Init
    stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Write) refers to stm32f401_discovery.o(.text.GYRO_IO_Write) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f401_discovery.o(.data.SpixTimeout) for SpixTimeout
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f401_discovery.o(.bss.SpiHandle) for SpiHandle
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit) for HAL_SPI_DeInit
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f401_discovery.o(.text.SPIx_Init) for SPIx_Init
    stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Read) refers to stm32f401_discovery.o(.text.GYRO_IO_Read) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.AUDIO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.AUDIO_IO_Init) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.text.AUDIO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.text.AUDIO_IO_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Init) refers to stm32f401_discovery.o(.text.AUDIO_IO_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for HAL_I2C_GetState
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    stm32f401_discovery.o(.ARM.exidx.text.I2Cx_Init) refers to stm32f401_discovery.o(.text.I2Cx_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_DeInit) refers to stm32f401_discovery.o(.text.AUDIO_IO_DeInit) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f401_discovery.o(.data.I2cxTimeout) for I2cxTimeout
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for HAL_I2C_DeInit
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Write) refers to stm32f401_discovery.o(.text.AUDIO_IO_Write) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f401_discovery.o(.data.I2cxTimeout) for I2cxTimeout
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for HAL_I2C_DeInit
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Read) refers to stm32f401_discovery.o(.text.AUDIO_IO_Read) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Init) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Init) refers to stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_ITConfig) refers to stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f401_discovery.o(.data.I2cxTimeout) for I2cxTimeout
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for HAL_I2C_DeInit
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Write) refers to stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f401_discovery.o(.data.I2cxTimeout) for I2cxTimeout
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for HAL_I2C_DeInit
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Read) refers to stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) for [Anonymous Symbol]
    diskio.o(.text.disk_status) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_status) refers to diskio.o(.text.disk_status) for [Anonymous Symbol]
    diskio.o(.text.disk_initialize) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_initialize) refers to diskio.o(.text.disk_initialize) for [Anonymous Symbol]
    diskio.o(.text.disk_read) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_read) refers to diskio.o(.text.disk_read) for [Anonymous Symbol]
    diskio.o(.text.disk_write) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_write) refers to diskio.o(.text.disk_write) for [Anonymous Symbol]
    diskio.o(.text.disk_ioctl) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_ioctl) refers to diskio.o(.text.disk_ioctl) for [Anonymous Symbol]
    diskio.o(.ARM.exidx.text.get_fattime) refers to diskio.o(.text.get_fattime) for [Anonymous Symbol]
    ff.o(.text.f_mount) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_mount) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.ARM.exidx.text.f_mount) refers to ff.o(.text.f_mount) for [Anonymous Symbol]
    ff.o(.text.find_volume) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.find_volume) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.find_volume) refers to diskio.o(.text.disk_initialize) for disk_initialize
    ff.o(.text.find_volume) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.find_volume) refers to ff.o(.text.find_volume) for [Anonymous Symbol]
    ff.o(.text.f_open) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_open) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_open) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_open) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_open) refers to ff.o(.text.dir_register) for dir_register
    ff.o(.text.f_open) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_open) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_open) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.f_open) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.f_open) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_open) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.f_open) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.ARM.exidx.text.f_open) refers to ff.o(.text.f_open) for [Anonymous Symbol]
    ff.o(.text.follow_path) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.follow_path) refers to unicode.o(.text.ff_convert) for ff_convert
    ff.o(.text.follow_path) refers to ff.o(.rodata.ExCvt) for ExCvt
    ff.o(.text.follow_path) refers to ff.o(.text.dir_find) for dir_find
    ff.o(.ARM.exidx.text.follow_path) refers to ff.o(.text.follow_path) for [Anonymous Symbol]
    ff.o(.text.dir_register) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_register) refers to ff.o(.text.dir_find) for dir_find
    ff.o(.text.dir_register) refers to ff.o(.text.dir_next) for dir_next
    ff.o(.text.dir_register) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_register) refers to ff.o(.text.dir_sdi) for dir_sdi
    ff.o(.ARM.exidx.text.dir_register) refers to ff.o(.text.dir_register) for [Anonymous Symbol]
    ff.o(.text.remove_chain) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.remove_chain) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.remove_chain) refers to ff.o(.text.remove_chain) for [Anonymous Symbol]
    ff.o(.text.move_window) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.move_window) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.ARM.exidx.text.move_window) refers to ff.o(.text.move_window) for [Anonymous Symbol]
    ff.o(.text.inc_lock) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.ARM.exidx.text.inc_lock) refers to ff.o(.text.inc_lock) for [Anonymous Symbol]
    ff.o(.text.get_fat) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.get_fat) refers to ff.o(.text.get_fat) for [Anonymous Symbol]
    ff.o(.text.f_read) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_read) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_read) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.text.f_read) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_read) refers to ff.o(.text.f_read) for [Anonymous Symbol]
    ff.o(.text.f_write) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_write) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.f_write) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.f_write) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.ARM.exidx.text.f_write) refers to ff.o(.text.f_write) for [Anonymous Symbol]
    ff.o(.text.create_chain) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.create_chain) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.ARM.exidx.text.create_chain) refers to ff.o(.text.create_chain) for [Anonymous Symbol]
    ff.o(.text.f_sync) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_sync) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.f_sync) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_sync) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.f_sync) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.ARM.exidx.text.f_sync) refers to ff.o(.text.f_sync) for [Anonymous Symbol]
    ff.o(.text.sync_fs) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.sync_fs) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.sync_fs) refers to diskio.o(.text.disk_ioctl) for disk_ioctl
    ff.o(.ARM.exidx.text.sync_fs) refers to ff.o(.text.sync_fs) for [Anonymous Symbol]
    ff.o(.text.f_close) refers to ff.o(.text.f_sync) for f_sync
    ff.o(.text.f_close) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_close) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.ARM.exidx.text.f_close) refers to ff.o(.text.f_close) for [Anonymous Symbol]
    ff.o(.text.f_lseek) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_lseek) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_lseek) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.f_lseek) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.text.f_lseek) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_lseek) refers to ff.o(.text.f_lseek) for [Anonymous Symbol]
    ff.o(.text.f_opendir) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_opendir) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_opendir) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_opendir) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_opendir) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_opendir) refers to ff.o(.text.inc_lock) for inc_lock
    ff.o(.ARM.exidx.text.f_opendir) refers to ff.o(.text.f_opendir) for [Anonymous Symbol]
    ff.o(.text.dir_sdi) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.dir_sdi) refers to ff.o(.text.dir_sdi) for [Anonymous Symbol]
    ff.o(.text.f_closedir) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_closedir) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.ARM.exidx.text.f_closedir) refers to ff.o(.text.f_closedir) for [Anonymous Symbol]
    ff.o(.text.f_readdir) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_readdir) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_readdir) refers to ff.o(.text.dir_read) for dir_read
    ff.o(.text.f_readdir) refers to ff.o(.text.get_fileinfo) for get_fileinfo
    ff.o(.text.f_readdir) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_readdir) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.f_readdir) refers to ff.o(.text.f_readdir) for [Anonymous Symbol]
    ff.o(.text.dir_read) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_read) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_read) refers to ff.o(.text.sum_sfn) for sum_sfn
    ff.o(.ARM.exidx.text.dir_read) refers to ff.o(.text.dir_read) for [Anonymous Symbol]
    ff.o(.text.get_fileinfo) refers to unicode.o(.text.ff_convert) for ff_convert
    ff.o(.ARM.exidx.text.get_fileinfo) refers to ff.o(.text.get_fileinfo) for [Anonymous Symbol]
    ff.o(.text.dir_next) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_next) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.dir_next) refers to ff.o(.text.sync_window) for sync_window
    ff.o(.text.dir_next) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.ARM.exidx.text.dir_next) refers to ff.o(.text.dir_next) for [Anonymous Symbol]
    ff.o(.text.f_stat) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_stat) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_stat) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_stat) refers to ff.o(.text.get_fileinfo) for get_fileinfo
    ff.o(.text.f_stat) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.ARM.exidx.text.f_stat) refers to ff.o(.text.f_stat) for [Anonymous Symbol]
    ff.o(.text.f_getfree) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_getfree) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_getfree) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.f_getfree) refers to ff.o(.text.f_getfree) for [Anonymous Symbol]
    ff.o(.text.f_truncate) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_truncate) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_truncate) refers to ff.o(.text.remove_chain) for remove_chain
    ff.o(.text.f_truncate) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_truncate) refers to ff.o(.text.f_truncate) for [Anonymous Symbol]
    ff.o(.text.f_unlink) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_unlink) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_unlink) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_unlink) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_unlink) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_unlink) refers to ff.o(.text.dir_remove) for dir_remove
    ff.o(.text.f_unlink) refers to ff.o(.text.remove_chain) for remove_chain
    ff.o(.text.f_unlink) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.text.f_unlink) refers to ff.o(.text.dir_sdi) for dir_sdi
    ff.o(.text.f_unlink) refers to ff.o(.text.dir_read) for dir_read
    ff.o(.ARM.exidx.text.f_unlink) refers to ff.o(.text.f_unlink) for [Anonymous Symbol]
    ff.o(.text.dir_remove) refers to ff.o(.text.dir_sdi) for dir_sdi
    ff.o(.text.dir_remove) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_remove) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.dir_remove) refers to ff.o(.text.dir_remove) for [Anonymous Symbol]
    ff.o(.text.f_mkdir) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_mkdir) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_mkdir) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_mkdir) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.f_mkdir) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_mkdir) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_mkdir) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_mkdir) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.f_mkdir) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.f_mkdir) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.f_mkdir) refers to ff.o(.text.dir_register) for dir_register
    ff.o(.text.f_mkdir) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.ARM.exidx.text.f_mkdir) refers to ff.o(.text.f_mkdir) for [Anonymous Symbol]
    ff.o(.text.sync_window) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.sync_window) refers to ff.o(.text.sync_window) for [Anonymous Symbol]
    ff.o(.text.f_rename) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_rename) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_rename) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_rename) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_rename) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_rename) refers to memcpya.o(.text) for __aeabi_memcpy
    ff.o(.text.f_rename) refers to ff.o(.text.dir_register) for dir_register
    ff.o(.text.f_rename) refers to ff.o(.text.dir_remove) for dir_remove
    ff.o(.text.f_rename) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.text.f_rename) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.f_rename) refers to ff.o(.text.f_rename) for [Anonymous Symbol]
    ff.o(.text.f_mkfs) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_mkfs) refers to diskio.o(.text.disk_initialize) for disk_initialize
    ff.o(.text.f_mkfs) refers to diskio.o(.text.disk_ioctl) for disk_ioctl
    ff.o(.text.f_mkfs) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.f_mkfs) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_mkfs) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_mkfs) refers to ff.o(.text.f_mkfs) for [Anonymous Symbol]
    ff.o(.text.dir_find) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_find) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_find) refers to unicode.o(.text.ff_wtoupper) for ff_wtoupper
    ff.o(.ARM.exidx.text.dir_find) refers to ff.o(.text.dir_find) for [Anonymous Symbol]
    ff.o(.ARM.exidx.text.sum_sfn) refers to ff.o(.text.sum_sfn) for [Anonymous Symbol]
    ff.o(.text.put_fat) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.put_fat) refers to ff.o(.text.put_fat) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_LinkDriverEx) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriverEx) refers to ff_gen_drv.o(.text.FATFS_LinkDriverEx) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_LinkDriver) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriver) refers to ff_gen_drv.o(.text.FATFS_LinkDriver) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_UnLinkDriverEx) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriverEx) refers to ff_gen_drv.o(.text.FATFS_UnLinkDriverEx) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_UnLinkDriver) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriver) refers to ff_gen_drv.o(.text.FATFS_UnLinkDriver) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_GetAttachedDriversNbr) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_GetAttachedDriversNbr) refers to ff_gen_drv.o(.text.FATFS_GetAttachedDriversNbr) for [Anonymous Symbol]
    syscall.o(.text.ff_memalloc) refers to malloc.o(i.malloc) for malloc
    syscall.o(.ARM.exidx.text.ff_memalloc) refers to syscall.o(.text.ff_memalloc) for [Anonymous Symbol]
    syscall.o(.text.ff_memfree) refers to malloc.o(i.free) for free
    syscall.o(.ARM.exidx.text.ff_memfree) refers to syscall.o(.text.ff_memfree) for [Anonymous Symbol]
    unicode.o(.text.ff_convert) refers to unicode.o(.rodata.Tbl) for Tbl
    unicode.o(.ARM.exidx.text.ff_convert) refers to unicode.o(.text.ff_convert) for [Anonymous Symbol]
    unicode.o(.text.ff_wtoupper) refers to unicode.o(.rodata.str2.2) for ff_wtoupper.cvt2
    unicode.o(.ARM.exidx.text.ff_wtoupper) refers to unicode.o(.text.ff_wtoupper) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_CoreInit) refers to stm32f4xx_ll_usb.o(.text.USB_CoreInit) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetTurnaroundTime) refers to stm32f4xx_ll_usb.o(.text.USB_SetTurnaroundTime) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EnableGlobalInt) refers to stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DisableGlobalInt) refers to stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetCurrentMode) refers to stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetMode) refers to stm32f4xx_ll_usb.o(.text.USB_GetMode) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevInit) refers to stm32f4xx_ll_usb.o(.text.USB_DevInit) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_SetDevSpeed) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushTxFifo) refers to stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushRxFifo) refers to stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetDevSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_GetDevSpeed) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateDedicatedEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateDedicatedEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_DeactivateEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateDedicatedEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_DeactivateDedicatedEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStartXfer) refers to stm32f4xx_ll_usb.o(.text.USB_EPStartXfer) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_WritePacket) refers to stm32f4xx_ll_usb.o(.text.USB_WritePacket) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStopXfer) refers to stm32f4xx_ll_usb.o(.text.USB_EPStopXfer) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadPacket) refers to stm32f4xx_ll_usb.o(.text.USB_ReadPacket) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPSetStall) refers to stm32f4xx_ll_usb.o(.text.USB_EPSetStall) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPClearStall) refers to stm32f4xx_ll_usb.o(.text.USB_EPClearStall) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopDevice) refers to stm32f4xx_ll_usb.o(.text.USB_StopDevice) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevAddress) refers to stm32f4xx_ll_usb.o(.text.USB_SetDevAddress) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevConnect) refers to stm32f4xx_ll_usb.o(.text.USB_DevConnect) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevDisconnect) refers to stm32f4xx_ll_usb.o(.text.USB_DevDisconnect) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadInterrupts) refers to stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadChInterrupts) refers to stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllOutEpInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevAllOutEpInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllInEpInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevAllInEpInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevOutEPInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevOutEPInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevInEPInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevInEPInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ClearInterrupts) refers to stm32f4xx_ll_usb.o(.text.USB_ClearInterrupts) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateSetup) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateSetup) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EP0_OutStart) refers to stm32f4xx_ll_usb.o(.text.USB_EP0_OutStart) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HostInit) refers to stm32f4xx_ll_usb.o(.text.USB_HostInit) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_InitFSLSPClkSel) refers to stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.text.USB_ResetPort) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ResetPort) refers to stm32f4xx_ll_usb.o(.text.USB_ResetPort) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DriveVbus) refers to stm32f4xx_ll_usb.o(.text.USB_DriveVbus) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetHostSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetCurrentFrame) refers to stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Init) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Init) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_StartXfer) refers to stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_ReadInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Halt) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Halt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DoPing) refers to stm32f4xx_ll_usb.o(.text.USB_DoPing) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopHost) refers to stm32f4xx_ll_usb.o(.text.USB_StopHost) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateRemoteWakeup) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateRemoteWakeup) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeActivateRemoteWakeup) refers to stm32f4xx_ll_usb.o(.text.USB_DeActivateRemoteWakeup) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_SelectPCROP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_SelectPCROP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_DeSelectPCROP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_DeSelectPCROP) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to usbh_conf.o(.text.HAL_HCD_MspInit) for HAL_HCD_MspInit
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_CoreInit) for USB_CoreInit
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode) for USB_SetCurrentMode
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_HostInit) for USB_HostInit
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for USB_GetHostSpeed
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Init) for USB_HC_Init
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_ClearHubInfo) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_ClearHubInfo) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Halt) for USB_HC_Halt
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Halt) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) refers to usbh_conf.o(.text.HAL_HCD_MspDeInit) for HAL_HCD_MspDeInit
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) refers to stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_DeInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspDeInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest) refers to stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer) for USB_HC_StartXfer
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SubmitRequest) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_GetMode) for USB_GetMode
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts) for USB_ReadInterrupts
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel) for USB_InitFSLSPClkSel
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) for HAL_HCD_Disconnect_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_SOF_Callback) for HAL_HCD_SOF_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt) for USB_HC_ReadInterrupt
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Halt) for USB_HC_Halt
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts) for USB_ReadChInterrupts
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) for HAL_HCD_HC_NotifyURBChange_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_Connect_Callback) for HAL_HCD_Connect_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) for HAL_HCD_PortDisabled_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_ReadPacket) for USB_ReadPacket
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) for HAL_HCD_PortEnabled_Callback
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_IRQHandler) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Disconnect_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_SOF_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_SOF_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_WKUP_IRQHandler) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_WKUP_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Connect_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Connect_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortEnabled_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortDisabled_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) refers to stm32f4xx_ll_usb.o(.text.USB_DriveVbus) for USB_DriveVbus
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) refers to stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt) for USB_EnableGlobalInt
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Start) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop) refers to stm32f4xx_ll_usb.o(.text.USB_StopHost) for USB_StopHost
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Stop) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort) refers to stm32f4xx_ll_usb.o(.text.USB_ResetPort) for USB_ResetPort
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_ResetPort) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetURBState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetXferCount) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame) refers to stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame) for USB_GetCurrentFrame
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentFrame) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for USB_GetHostSpeed
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentSpeed) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SetHubInfo) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for USB_GetHostSpeed
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SetHubInfo) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SetHubInfo) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Init) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Init) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Init) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspInit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DeInit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspDeInit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_TxISR_16BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_TxISR_8BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BITCRC) for SPI_RxISR_16BITCRC
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BITCRC) for SPI_RxISR_8BITCRC
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BITCRC) for SPI_2linesRxISR_16BITCRC
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BITCRC) for SPI_2linesRxISR_8BITCRC
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAError) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAError) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_AbortTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortTx_ISR) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_AbortRx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_AbortCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAPause) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAResume) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAStop) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_ErrorCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxHalfCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxHalfCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxHalfCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetState) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetError) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BITCRC) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BITCRC) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BITCRC) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BITCRC) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) for [Anonymous Symbol]
    usbh_pipes.o(.text.USBH_OpenPipe) refers to usbh_conf.o(.text.USBH_LL_OpenPipe) for USBH_LL_OpenPipe
    usbh_pipes.o(.ARM.exidx.text.USBH_OpenPipe) refers to usbh_pipes.o(.text.USBH_OpenPipe) for [Anonymous Symbol]
    usbh_pipes.o(.text.USBH_ClosePipe) refers to usbh_conf.o(.text.USBH_LL_ClosePipe) for USBH_LL_ClosePipe
    usbh_pipes.o(.ARM.exidx.text.USBH_ClosePipe) refers to usbh_pipes.o(.text.USBH_ClosePipe) for [Anonymous Symbol]
    usbh_pipes.o(.ARM.exidx.text.USBH_AllocPipe) refers to usbh_pipes.o(.text.USBH_AllocPipe) for [Anonymous Symbol]
    usbh_pipes.o(.ARM.exidx.text.USBH_FreePipe) refers to usbh_pipes.o(.text.USBH_FreePipe) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Init) refers to memseta.o(.text) for __aeabi_memclr4
    usbh_core.o(.text.USBH_Init) refers to usbh_conf.o(.text.USBH_LL_Init) for USBH_LL_Init
    usbh_core.o(.ARM.exidx.text.USBH_Init) refers to usbh_core.o(.text.USBH_Init) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_DeInit) refers to memseta.o(.text) for __aeabi_memclr4
    usbh_core.o(.text.USBH_DeInit) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.ARM.exidx.text.USBH_DeInit) refers to usbh_core.o(.text.USBH_DeInit) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_RegisterClass) refers to usbh_core.o(.text.USBH_RegisterClass) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_SelectInterface) refers to usbh_core.o(.text.USBH_SelectInterface) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_GetActiveClass) refers to usbh_core.o(.text.USBH_GetActiveClass) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_FindInterface) refers to usbh_core.o(.text.USBH_FindInterface) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_FindInterfaceIndex) refers to usbh_core.o(.text.USBH_FindInterfaceIndex) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Start) refers to usbh_conf.o(.text.USBH_LL_Start) for USBH_LL_Start
    usbh_core.o(.text.USBH_Start) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.ARM.exidx.text.USBH_Start) refers to usbh_core.o(.text.USBH_Start) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Stop) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.text.USBH_Stop) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.text.USBH_Stop) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_Stop) refers to usbh_core.o(.text.USBH_Stop) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_ReEnumerate) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.text.USBH_ReEnumerate) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.text.USBH_ReEnumerate) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_ReEnumerate) refers to usbh_core.o(.text.USBH_ReEnumerate) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_IsPortEnabled) refers to usbh_core.o(.text.USBH_IsPortEnabled) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_Delay) for USBH_Delay
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_ResetPort) for USBH_LL_ResetPort
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_Get_DevDesc) for USBH_Get_DevDesc
    usbh_core.o(.text.USBH_Process) refers to usbh_pipes.o(.text.USBH_OpenPipe) for USBH_OpenPipe
    usbh_core.o(.text.USBH_Process) refers to memseta.o(.text) for __aeabi_memclr4
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_Start) for USBH_LL_Start
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_GetSpeed) for USBH_LL_GetSpeed
    usbh_core.o(.text.USBH_Process) refers to usbh_pipes.o(.text.USBH_AllocPipe) for USBH_AllocPipe
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_SetCfg) for USBH_SetCfg
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_SetFeature) for USBH_SetFeature
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_Get_CfgDesc) for USBH_Get_CfgDesc
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_SetAddress) for USBH_SetAddress
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_Get_StringDesc) for USBH_Get_StringDesc
    usbh_core.o(.text.USBH_Process) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_Process) refers to usbh_core.o(.text.USBH_Process) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_SetTimer) refers to usbh_core.o(.text.USBH_LL_SetTimer) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_IncTimer) refers to usbh_core.o(.text.USBH_LL_IncTimer) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_PortEnabled) refers to usbh_core.o(.text.USBH_LL_PortEnabled) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_PortDisabled) refers to usbh_core.o(.text.USBH_LL_PortDisabled) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_Connect) refers to usbh_core.o(.text.USBH_LL_Connect) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_LL_Disconnect) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.text.USBH_LL_Disconnect) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_LL_Disconnect) refers to usbh_core.o(.text.USBH_LL_Disconnect) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_CtlSendSetup) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendSetup) refers to usbh_ioreq.o(.text.USBH_CtlSendSetup) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_CtlSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendData) refers to usbh_ioreq.o(.text.USBH_CtlSendData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_CtlReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_CtlReceiveData) refers to usbh_ioreq.o(.text.USBH_CtlReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_BulkSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_BulkSendData) refers to usbh_ioreq.o(.text.USBH_BulkSendData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_BulkReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_BulkReceiveData) refers to usbh_ioreq.o(.text.USBH_BulkReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_InterruptReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptReceiveData) refers to usbh_ioreq.o(.text.USBH_InterruptReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_InterruptSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptSendData) refers to usbh_ioreq.o(.text.USBH_InterruptSendData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_IsocReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_IsocReceiveData) refers to usbh_ioreq.o(.text.USBH_IsocReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_IsocSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_IsocSendData) refers to usbh_ioreq.o(.text.USBH_IsocSendData) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_Get_DevDesc) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_DevDesc) refers to usbh_ctlreq.o(.text.USBH_Get_DevDesc) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_GetDescriptor) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_GetDescriptor) refers to usbh_ctlreq.o(.text.USBH_GetDescriptor) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_Get_CfgDesc) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_CfgDesc) refers to usbh_ctlreq.o(.text.USBH_Get_CfgDesc) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_Get_StringDesc) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_StringDesc) refers to usbh_ctlreq.o(.text.USBH_Get_StringDesc) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_ioreq.o(.text.USBH_CtlSendSetup) for USBH_CtlSendSetup
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_ioreq.o(.text.USBH_CtlSendData) for USBH_CtlSendData
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_conf.o(.text.USBH_LL_GetURBState) for USBH_LL_GetURBState
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_ioreq.o(.text.USBH_CtlReceiveData) for USBH_CtlReceiveData
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_ctlreq.o(.ARM.exidx.text.USBH_CtlReq) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetAddress) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetAddress) refers to usbh_ctlreq.o(.text.USBH_SetAddress) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetCfg) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetCfg) refers to usbh_ctlreq.o(.text.USBH_SetCfg) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetInterface) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetInterface) refers to usbh_ctlreq.o(.text.USBH_SetInterface) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetFeature) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetFeature) refers to usbh_ctlreq.o(.text.USBH_SetFeature) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_ClrFeature) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_ClrFeature) refers to usbh_ctlreq.o(.text.USBH_ClrFeature) for [Anonymous Symbol]
    usbh_ctlreq.o(.ARM.exidx.text.USBH_GetNextDesc) refers to usbh_ctlreq.o(.text.USBH_GetNextDesc) for [Anonymous Symbol]
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_initialize) refers to usbh_diskio_dma.o(.text.USBH_initialize) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_status) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    usbh_diskio_dma.o(.text.USBH_status) refers to usbh_msc.o(.text.USBH_MSC_UnitIsReady) for USBH_MSC_UnitIsReady
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_status) refers to usbh_diskio_dma.o(.text.USBH_status) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_read) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    usbh_diskio_dma.o(.text.USBH_read) refers to usbh_diskio_dma.o(.bss.scratch) for scratch
    usbh_diskio_dma.o(.text.USBH_read) refers to usbh_msc.o(.text.USBH_MSC_Read) for USBH_MSC_Read
    usbh_diskio_dma.o(.text.USBH_read) refers to memcpya.o(.text) for __aeabi_memcpy
    usbh_diskio_dma.o(.text.USBH_read) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for USBH_MSC_GetLUNInfo
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_read) refers to usbh_diskio_dma.o(.text.USBH_read) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_write) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    usbh_diskio_dma.o(.text.USBH_write) refers to usbh_diskio_dma.o(.bss.scratch) for scratch
    usbh_diskio_dma.o(.text.USBH_write) refers to memcpya.o(.text) for __aeabi_memcpy
    usbh_diskio_dma.o(.text.USBH_write) refers to usbh_msc.o(.text.USBH_MSC_Write) for USBH_MSC_Write
    usbh_diskio_dma.o(.text.USBH_write) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for USBH_MSC_GetLUNInfo
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_write) refers to usbh_diskio_dma.o(.text.USBH_write) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_ioctl) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    usbh_diskio_dma.o(.text.USBH_ioctl) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for USBH_MSC_GetLUNInfo
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_ioctl) refers to usbh_diskio_dma.o(.text.USBH_ioctl) for [Anonymous Symbol]
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_initialize) for USBH_initialize
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_status) for USBH_status
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_read) for USBH_read
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_write) for USBH_write
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_ioctl) for USBH_ioctl
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to usbh_conf.o(.rodata.str1.1) for .Lstr
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to puts.o(i.puts) for puts
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspInit) refers to usbh_conf.o(.text.HAL_HCD_MspInit) for [Anonymous Symbol]
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspDeInit) refers to usbh_conf.o(.text.HAL_HCD_MspDeInit) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_SOF_Callback) refers to usbh_conf.o(.bss.debug_sof_count) for debug_sof_count
    usbh_conf.o(.text.HAL_HCD_SOF_Callback) refers to usbh_core.o(.text.USBH_LL_IncTimer) for USBH_LL_IncTimer
    usbh_conf.o(.text.HAL_HCD_SOF_Callback) refers to printf4.o(i.__0printf$4) for __2printf
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_SOF_Callback) refers to usbh_conf.o(.text.HAL_HCD_SOF_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_Connect_Callback) refers to usbh_conf.o(.bss.debug_connect_count) for debug_connect_count
    usbh_conf.o(.text.HAL_HCD_Connect_Callback) refers to printf4.o(i.__0printf$4) for __2printf
    usbh_conf.o(.text.HAL_HCD_Connect_Callback) refers to puts.o(i.puts) for puts
    usbh_conf.o(.text.HAL_HCD_Connect_Callback) refers to usbh_core.o(.text.USBH_LL_Connect) for USBH_LL_Connect
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_Connect_Callback) refers to usbh_conf.o(.text.HAL_HCD_Connect_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) refers to usbh_conf.o(.bss.debug_disconnect_count) for debug_disconnect_count
    usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) refers to printf4.o(i.__0printf$4) for __2printf
    usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) refers to puts.o(i.puts) for puts
    usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) refers to usbh_core.o(.text.USBH_LL_Disconnect) for USBH_LL_Disconnect
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback) refers to usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) refers to puts.o(i.puts) for puts
    usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) refers to usbh_core.o(.text.USBH_LL_PortEnabled) for USBH_LL_PortEnabled
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback) refers to usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) refers to puts.o(i.puts) for puts
    usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) refers to usbh_core.o(.text.USBH_LL_PortDisabled) for USBH_LL_PortDisabled
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback) refers to usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) refers to usbh_conf.o(.bss.debug_urb_count) for debug_urb_count
    usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) refers to usbh_conf.o(.rodata.str1.1) for .L.str.23
    usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) refers to printf4.o(i.__0printf$4) for __2printf
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback) refers to usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_Init) refers to usbh_conf.o(.rodata.str1.1) for .Lstr.56
    usbh_conf.o(.text.USBH_LL_Init) refers to puts.o(i.puts) for puts
    usbh_conf.o(.text.USBH_LL_Init) refers to usbh_conf.o(.bss.hhcd) for hhcd
    usbh_conf.o(.text.USBH_LL_Init) refers to printf4.o(i.__0printf$4) for __2printf
    usbh_conf.o(.text.USBH_LL_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) for HAL_HCD_Init
    usbh_conf.o(.text.USBH_LL_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame) for HAL_HCD_GetCurrentFrame
    usbh_conf.o(.text.USBH_LL_Init) refers to usbh_core.o(.text.USBH_LL_SetTimer) for USBH_LL_SetTimer
    usbh_conf.o(.ARM.exidx.text.USBH_LL_Init) refers to usbh_conf.o(.text.USBH_LL_Init) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_DeInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) for HAL_HCD_DeInit
    usbh_conf.o(.ARM.exidx.text.USBH_LL_DeInit) refers to usbh_conf.o(.text.USBH_LL_DeInit) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_Start) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) for HAL_HCD_Start
    usbh_conf.o(.ARM.exidx.text.USBH_LL_Start) refers to usbh_conf.o(.text.USBH_LL_Start) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_Stop) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop) for HAL_HCD_Stop
    usbh_conf.o(.ARM.exidx.text.USBH_LL_Stop) refers to usbh_conf.o(.text.USBH_LL_Stop) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetSpeed) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed) for HAL_HCD_GetCurrentSpeed
    usbh_conf.o(.text.USBH_LL_GetSpeed) refers to usbh_conf.o(.rodata.str1.1) for .L.str.38
    usbh_conf.o(.text.USBH_LL_GetSpeed) refers to printf4.o(i.__0printf$4) for __2printf
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetSpeed) refers to usbh_conf.o(.text.USBH_LL_GetSpeed) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_ResetPort) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort) for HAL_HCD_ResetPort
    usbh_conf.o(.ARM.exidx.text.USBH_LL_ResetPort) refers to usbh_conf.o(.text.USBH_LL_ResetPort) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetLastXferSize) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount) for HAL_HCD_HC_GetXferCount
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetLastXferSize) refers to usbh_conf.o(.text.USBH_LL_GetLastXferSize) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_OpenPipe) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) for HAL_HCD_HC_Init
    usbh_conf.o(.ARM.exidx.text.USBH_LL_OpenPipe) refers to usbh_conf.o(.text.USBH_LL_OpenPipe) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_ClosePipe) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt) for HAL_HCD_HC_Halt
    usbh_conf.o(.ARM.exidx.text.USBH_LL_ClosePipe) refers to usbh_conf.o(.text.USBH_LL_ClosePipe) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_SubmitURB) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest) for HAL_HCD_HC_SubmitRequest
    usbh_conf.o(.ARM.exidx.text.USBH_LL_SubmitURB) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetURBState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState) for HAL_HCD_HC_GetURBState
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetURBState) refers to usbh_conf.o(.text.USBH_LL_GetURBState) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_DriverVBUS) refers to usbh_conf.o(.rodata.str1.1) for .Lstr.60
    usbh_conf.o(.text.USBH_LL_DriverVBUS) refers to puts.o(i.puts) for puts
    usbh_conf.o(.text.USBH_LL_DriverVBUS) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    usbh_conf.o(.text.USBH_LL_DriverVBUS) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    usbh_conf.o(.ARM.exidx.text.USBH_LL_DriverVBUS) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_SetToggle) refers to usbh_conf.o(.bss.hhcd) for hhcd
    usbh_conf.o(.ARM.exidx.text.USBH_LL_SetToggle) refers to usbh_conf.o(.text.USBH_LL_SetToggle) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetToggle) refers to usbh_conf.o(.bss.hhcd) for hhcd
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetToggle) refers to usbh_conf.o(.text.USBH_LL_GetToggle) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    usbh_conf.o(.ARM.exidx.text.USBH_Delay) refers to usbh_conf.o(.text.USBH_Delay) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.__io_putchar) refers to main.o(.text.__io_putchar) for [Anonymous Symbol]
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to main.o(.rodata.str1.1) for .Lstr
    main.o(.text.main) refers to puts.o(i.puts) for puts
    main.o(.text.main) refers to stm32f401_discovery.o(.text.BSP_LED_Init) for BSP_LED_Init
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    main.o(.text.main) refers to printf4.o(i.__0printf$4) for __2printf
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for HAL_GetTickFreq
    main.o(.text.main) refers to main.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    main.o(.text.main) refers to usbh_diskio_dma.o(.rodata.USBH_Driver) for USBH_Driver
    main.o(.text.main) refers to ff_gen_drv.o(.text.FATFS_LinkDriver) for FATFS_LinkDriver
    main.o(.text.main) refers to stm32f401_discovery.o(.text.BSP_LED_On) for BSP_LED_On
    main.o(.text.main) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    main.o(.text.main) refers to main.o(.text.USBH_UserProcess) for USBH_UserProcess
    main.o(.text.main) refers to usbh_core.o(.text.USBH_Init) for USBH_Init
    main.o(.text.main) refers to usbh_msc.o(.data.USBH_msc) for USBH_msc
    main.o(.text.main) refers to usbh_core.o(.text.USBH_RegisterClass) for USBH_RegisterClass
    main.o(.text.main) refers to usbh_core.o(.text.USBH_Start) for USBH_Start
    main.o(.text.main) refers to main.o(.rodata..L__const.MSC_Application.wtext) for .L__const.MSC_Application.wtext
    main.o(.text.main) refers to main.o(.data.Debug_PrintUSBHostState.last_state) for Debug_PrintUSBHostState.last_state
    main.o(.text.main) refers to usbh_core.o(.text.USBH_Process) for USBH_Process
    main.o(.text.main) refers to main.o(.rodata..Lswitch.table.main) for .Lswitch.table.main
    main.o(.text.main) refers to main.o(.bss.USBDISKFatFs) for USBDISKFatFs
    main.o(.text.main) refers to ff.o(.text.f_mount) for f_mount
    main.o(.text.main) refers to main.o(.bss.MyFile) for MyFile
    main.o(.text.main) refers to ff.o(.text.f_open) for f_open
    main.o(.text.main) refers to ff.o(.text.f_write) for f_write
    main.o(.text.main) refers to ff.o(.text.f_close) for f_close
    main.o(.text.main) refers to ff.o(.text.f_read) for f_read
    main.o(.text.main) refers to ff_gen_drv.o(.text.FATFS_UnLinkDriver) for FATFS_UnLinkDriver
    main.o(.text.main) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.USBH_UserProcess) refers to puts.o(i.puts) for puts
    main.o(.text.USBH_UserProcess) refers to main.o(.rodata.str1.1) for .Lstr.95
    main.o(.text.USBH_UserProcess) refers to main.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    main.o(.text.USBH_UserProcess) refers to stm32f401_discovery.o(.text.BSP_LED_Off) for BSP_LED_Off
    main.o(.text.USBH_UserProcess) refers to ff.o(.text.f_mount) for f_mount
    main.o(.text.USBH_UserProcess) refers to printf4.o(i.__0printf$4) for __2printf
    main.o(.ARM.exidx.text.USBH_UserProcess) refers to main.o(.text.USBH_UserProcess) for [Anonymous Symbol]
    main.o(.text.Error_Handler) refers to main.o(.rodata.str1.1) for .Lstr.113
    main.o(.text.Error_Handler) refers to puts.o(i.puts) for puts
    main.o(.text.Error_Handler) refers to stm32f401_discovery.o(.text.BSP_LED_On) for BSP_LED_On
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    main.o(.rodata..Lswitch.table.main) refers to main.o(.rodata.str1.1) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.OTG_FS_IRQHandler) refers to usbh_conf.o(.bss.hhcd) for hhcd
    stm32f4xx_it.o(.text.OTG_FS_IRQHandler) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) for HAL_HCD_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.OTG_FS_IRQHandler) refers to stm32f4xx_it.o(.text.OTG_FS_IRQHandler) for [Anonymous Symbol]
    usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_Reset) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_Reset) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_Reset) for [Anonymous Symbol]
    usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_GetMaxLUN) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN) for [Anonymous Symbol]
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Init) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Init) for [Anonymous Symbol]
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ioreq.o(.text.USBH_BulkSendData) for USBH_BulkSendData
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ctlreq.o(.text.USBH_ClrFeature) for USBH_ClrFeature
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_GetToggle) for USBH_LL_GetToggle
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_SetToggle) for USBH_LL_SetToggle
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ioreq.o(.text.USBH_BulkReceiveData) for USBH_BulkReceiveData
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_GetURBState) for USBH_LL_GetURBState
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_GetLastXferSize) for USBH_LL_GetLastXferSize
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Process) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_core.o(.text.USBH_FindInterface) for USBH_FindInterface
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_core.o(.text.USBH_SelectInterface) for USBH_SelectInterface
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to calloc.o(.text) for calloc
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_pipes.o(.text.USBH_AllocPipe) for USBH_AllocPipe
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Init) for USBH_MSC_BOT_Init
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_pipes.o(.text.USBH_OpenPipe) for USBH_OpenPipe
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_conf.o(.text.USBH_LL_SetToggle) for USBH_LL_SetToggle
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceInit) refers to usbh_msc.o(.text.USBH_MSC_InterfaceInit) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) refers to usbh_pipes.o(.text.USBH_ClosePipe) for USBH_ClosePipe
    usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) refers to malloc.o(i.free) for free
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceDeInit) refers to usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_ClassRequest) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN) for USBH_MSC_BOT_REQ_GetMaxLUN
    usbh_msc.o(.text.USBH_MSC_ClassRequest) refers to usbh_ctlreq.o(.text.USBH_ClrFeature) for USBH_ClrFeature
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_ClassRequest) refers to usbh_msc.o(.text.USBH_MSC_ClassRequest) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity) for USBH_MSC_SCSI_ReadCapacity
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady) for USBH_MSC_SCSI_TestUnitReady
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) for USBH_MSC_SCSI_Inquiry
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) for USBH_MSC_SCSI_RequestSense
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_Process) refers to usbh_msc.o(.text.USBH_MSC_Process) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_SOFProcess) refers to usbh_msc.o(.text.USBH_MSC_SOFProcess) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_IsReady) refers to usbh_msc.o(.text.USBH_MSC_IsReady) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetMaxLUN) refers to usbh_msc.o(.text.USBH_MSC_GetMaxLUN) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_UnitIsReady) refers to usbh_msc.o(.text.USBH_MSC_UnitIsReady) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetLUNInfo) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_Read) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) for USBH_MSC_SCSI_Read
    usbh_msc.o(.text.USBH_MSC_Read) refers to usbh_msc.o(.text.USBH_MSC_RdWrProcess) for USBH_MSC_RdWrProcess
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_Read) refers to usbh_msc.o(.text.USBH_MSC_Read) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_RdWrProcess) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) for USBH_MSC_SCSI_Read
    usbh_msc.o(.text.USBH_MSC_RdWrProcess) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) for USBH_MSC_SCSI_Write
    usbh_msc.o(.text.USBH_MSC_RdWrProcess) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) for USBH_MSC_SCSI_RequestSense
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_RdWrProcess) refers to usbh_msc.o(.text.USBH_MSC_RdWrProcess) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_Write) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) for USBH_MSC_SCSI_Write
    usbh_msc.o(.text.USBH_MSC_Write) refers to usbh_msc.o(.text.USBH_MSC_RdWrProcess) for USBH_MSC_RdWrProcess
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_Write) refers to usbh_msc.o(.text.USBH_MSC_Write) for [Anonymous Symbol]
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.rodata.str1.1) for [Anonymous Symbol]
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_InterfaceInit) for USBH_MSC_InterfaceInit
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) for USBH_MSC_InterfaceDeInit
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_ClassRequest) for USBH_MSC_ClassRequest
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_Process) for USBH_MSC_Process
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_SOFProcess) for USBH_MSC_SOFProcess
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_TestUnitReady) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_ReadCapacity) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) refers to memseta.o(.text) for __aeabi_memclr
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Inquiry) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_RequestSense) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Write) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Read) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) for [Anonymous Symbol]
    startup_stm32f401xc.o(RESET) refers to startup_stm32f401xc.o(STACK) for __initial_sp
    startup_stm32f401xc.o(RESET) refers to startup_stm32f401xc.o(.text) for Reset_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.OTG_FS_IRQHandler) for OTG_FS_IRQHandler
    startup_stm32f401xc.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f401xc.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    puts.o(i.puts) refers to fputc.o(i.fputc) for fputc
    puts.o(i.puts) refers to stdout.o(.data) for __stdout
    puts_e.o(.text) refers to fputc.o(i.fputc) for fputc
    puts_e.o(.text) refers to stdout.o(.data) for __stdout
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to fputc.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to stdout.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to fputc.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to stdout.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to fputc.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to stdout.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to fputc.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to stdout.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to fputc.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to stdout.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to fputc.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to stdout.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to fputc.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to stdout.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to fputc.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to stdout.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to fputc.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to stdout.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to fputc.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to stdout.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f401xc.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f401xc.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f401xc.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f401xc.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    calloc.o(.text) refers to malloc.o(i.malloc) for malloc
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f401xc.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f401xc.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing stm32f401_discovery.o(.text), (0 bytes).
    Removing stm32f401_discovery.o(.text.BSP_GetVersion), (10 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_GetVersion), (8 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Init), (8 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_On), (8 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Off), (8 bytes).
    Removing stm32f401_discovery.o(.text.BSP_LED_Toggle), (30 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Toggle), (8 bytes).
    Removing stm32f401_discovery.o(.text.BSP_PB_Init), (134 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_PB_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.BSP_PB_GetState), (22 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_PB_GetState), (8 bytes).
    Removing stm32f401_discovery.o(.text.GYRO_IO_Init), (122 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.SPIx_Init), (156 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.SPIx_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.GYRO_IO_Write), (206 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Write), (8 bytes).
    Removing stm32f401_discovery.o(.text.GYRO_IO_Read), (206 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Read), (8 bytes).
    Removing stm32f401_discovery.o(.text.AUDIO_IO_Init), (104 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.I2Cx_Init), (202 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.I2Cx_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.AUDIO_IO_DeInit), (2 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_DeInit), (8 bytes).
    Removing stm32f401_discovery.o(.text.AUDIO_IO_Write), (76 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Write), (8 bytes).
    Removing stm32f401_discovery.o(.text.AUDIO_IO_Read), (80 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Read), (8 bytes).
    Removing stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Init), (68 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig), (84 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_ITConfig), (8 bytes).
    Removing stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write), (76 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Write), (8 bytes).
    Removing stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read), (80 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Read), (8 bytes).
    Removing stm32f401_discovery.o(.data.BUTTON_PORT), (4 bytes).
    Removing stm32f401_discovery.o(.rodata.BUTTON_PIN), (2 bytes).
    Removing stm32f401_discovery.o(.rodata.BUTTON_IRQn), (1 bytes).
    Removing stm32f401_discovery.o(.data.I2cxTimeout), (4 bytes).
    Removing stm32f401_discovery.o(.data.SpixTimeout), (4 bytes).
    Removing stm32f401_discovery.o(.bss.SpiHandle), (88 bytes).
    Removing stm32f401_discovery.o(.bss.I2cHandle), (84 bytes).
    Removing diskio.o(.text), (0 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_status), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_initialize), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_read), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_write), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_ioctl), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.get_fattime), (8 bytes).
    Removing ff.o(.text), (0 bytes).
    Removing ff.o(.ARM.exidx.text.f_mount), (8 bytes).
    Removing ff.o(.ARM.exidx.text.find_volume), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_open), (8 bytes).
    Removing ff.o(.ARM.exidx.text.follow_path), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_register), (8 bytes).
    Removing ff.o(.text.remove_chain), (160 bytes).
    Removing ff.o(.ARM.exidx.text.remove_chain), (8 bytes).
    Removing ff.o(.ARM.exidx.text.move_window), (8 bytes).
    Removing ff.o(.text.inc_lock), (162 bytes).
    Removing ff.o(.ARM.exidx.text.inc_lock), (8 bytes).
    Removing ff.o(.ARM.exidx.text.get_fat), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_read), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_write), (8 bytes).
    Removing ff.o(.ARM.exidx.text.create_chain), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_sync), (8 bytes).
    Removing ff.o(.ARM.exidx.text.sync_fs), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_close), (8 bytes).
    Removing ff.o(.text.f_lseek), (740 bytes).
    Removing ff.o(.ARM.exidx.text.f_lseek), (8 bytes).
    Removing ff.o(.text.f_opendir), (310 bytes).
    Removing ff.o(.ARM.exidx.text.f_opendir), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_sdi), (8 bytes).
    Removing ff.o(.text.f_closedir), (104 bytes).
    Removing ff.o(.ARM.exidx.text.f_closedir), (8 bytes).
    Removing ff.o(.text.f_readdir), (408 bytes).
    Removing ff.o(.ARM.exidx.text.f_readdir), (8 bytes).
    Removing ff.o(.text.dir_read), (832 bytes).
    Removing ff.o(.ARM.exidx.text.dir_read), (8 bytes).
    Removing ff.o(.text.get_fileinfo), (490 bytes).
    Removing ff.o(.ARM.exidx.text.get_fileinfo), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_next), (8 bytes).
    Removing ff.o(.text.f_stat), (96 bytes).
    Removing ff.o(.ARM.exidx.text.f_stat), (8 bytes).
    Removing ff.o(.text.f_getfree), (264 bytes).
    Removing ff.o(.ARM.exidx.text.f_getfree), (8 bytes).
    Removing ff.o(.text.f_truncate), (212 bytes).
    Removing ff.o(.ARM.exidx.text.f_truncate), (8 bytes).
    Removing ff.o(.text.f_unlink), (272 bytes).
    Removing ff.o(.ARM.exidx.text.f_unlink), (8 bytes).
    Removing ff.o(.text.dir_remove), (244 bytes).
    Removing ff.o(.ARM.exidx.text.dir_remove), (8 bytes).
    Removing ff.o(.text.f_mkdir), (730 bytes).
    Removing ff.o(.ARM.exidx.text.f_mkdir), (8 bytes).
    Removing ff.o(.ARM.exidx.text.sync_window), (8 bytes).
    Removing ff.o(.text.f_rename), (594 bytes).
    Removing ff.o(.ARM.exidx.text.f_rename), (8 bytes).
    Removing ff.o(.text.f_mkfs), (2016 bytes).
    Removing ff.o(.ARM.exidx.text.f_mkfs), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_find), (8 bytes).
    Removing ff.o(.text.sum_sfn), (130 bytes).
    Removing ff.o(.ARM.exidx.text.sum_sfn), (8 bytes).
    Removing ff.o(.ARM.exidx.text.put_fat), (8 bytes).
    Removing ff_gen_drv.o(.text), (0 bytes).
    Removing ff_gen_drv.o(.text.FATFS_LinkDriverEx), (80 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriverEx), (8 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriver), (8 bytes).
    Removing ff_gen_drv.o(.text.FATFS_UnLinkDriverEx), (48 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriverEx), (8 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriver), (8 bytes).
    Removing ff_gen_drv.o(.text.FATFS_GetAttachedDriversNbr), (12 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_GetAttachedDriversNbr), (8 bytes).
    Removing syscall.o(.text), (0 bytes).
    Removing syscall.o(.ARM.exidx.text.ff_memalloc), (8 bytes).
    Removing syscall.o(.ARM.exidx.text.ff_memfree), (8 bytes).
    Removing unicode.o(.text), (0 bytes).
    Removing unicode.o(.ARM.exidx.text.ff_convert), (8 bytes).
    Removing unicode.o(.ARM.exidx.text.ff_wtoupper), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1236 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (466 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text), (0 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_CoreInit), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_SetTurnaroundTime), (280 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetTurnaroundTime), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EnableGlobalInt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DisableGlobalInt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetCurrentMode), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetMode), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DevInit), (1206 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevInit), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_SetDevSpeed), (16 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevSpeed), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushTxFifo), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushRxFifo), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_GetDevSpeed), (26 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetDevSpeed), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateEndpoint), (130 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateDedicatedEndpoint), (116 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateDedicatedEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DeactivateEndpoint), (130 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DeactivateDedicatedEndpoint), (100 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateDedicatedEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPStartXfer), (570 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStartXfer), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_WritePacket), (106 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_WritePacket), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPStopXfer), (270 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStopXfer), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadPacket), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPSetStall), (62 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPSetStall), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPClearStall), (82 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPClearStall), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_StopDevice), (510 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopDevice), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_SetDevAddress), (34 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevAddress), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DevConnect), (30 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevConnect), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DevDisconnect), (30 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevDisconnect), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadChInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevAllOutEpInterrupt), (14 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllOutEpInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevAllInEpInterrupt), (14 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllInEpInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevOutEPInterrupt), (16 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevOutEPInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevInEPInterrupt), (36 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevInEPInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ClearInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ClearInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateSetup), (32 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateSetup), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EP0_OutStart), (90 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EP0_OutStart), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HostInit), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_InitFSLSPClkSel), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ResetPort), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DriveVbus), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetHostSpeed), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetCurrentFrame), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Init), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_StartXfer), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_ReadInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Halt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DoPing), (32 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DoPing), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopHost), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateRemoteWakeup), (24 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateRemoteWakeup), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DeActivateRemoteWakeup), (18 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeActivateRemoteWakeup), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (218 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (274 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (240 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (340 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Init), (354 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT), (162 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort), (142 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT), (36 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler), (452 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (50 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.rodata.cst8), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (394 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (70 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram), (78 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBGetConfig), (14 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_SelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_SelectPCROP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_DeSelectPCROP), (8 bytes).
    Removing stm32f4xx_hal_dcmi.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (70 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init), (356 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit), (52 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (476 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout), (458 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (796 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (378 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAError), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite), (200 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read), (600 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (282 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (298 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (514 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (576 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady), (532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (324 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (598 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort), (286 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (448 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (88 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_ITError), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler), (1540 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE), (178 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF), (190 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE), (318 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (214 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text), (0 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Init), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspInit), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspInit), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Init), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_ClearHubInfo), (18 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_ClearHubInfo), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Halt), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit), (36 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_DeInit), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SubmitRequest), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_Disconnect_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_SOF_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_SOF_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_WKUP_IRQHandler), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_WKUP_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_Connect_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Connect_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortEnabled_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortDisabled_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_NotifyURBChange_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Start), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Stop), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_ResetPort), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetState), (6 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetState), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetURBState), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetXferCount), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetState), (14 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetState), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentFrame), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentSpeed), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SetHubInfo), (64 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SetHubInfo), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text), (0 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Init), (194 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Init), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspInit), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DeInit), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit), (692 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction), (184 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_EndRxTxTransaction), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive), (682 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive), (706 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout), (292 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_WaitFlagStateUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_IT), (196 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_TxISR_16BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_16BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_TxISR_8BIT), (54 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_8BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT), (388 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT), (236 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT), (76 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT), (68 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_16BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT), (70 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT), (76 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_8BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA), (246 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt), (268 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAError), (30 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAError), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA), (282 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA), (332 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_DMA), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt), (236 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt), (316 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort), (480 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_AbortTx_ISR), (26 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_AbortTx_ISR), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR), (146 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_AbortRx_ISR), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT), (458 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback), (184 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback), (272 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAPause), (32 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAResume), (32 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAStop), (74 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler), (302 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError), (12 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_GetState), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetState), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_GetError), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetError), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BITCRC), (42 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BITCRC), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR), (422 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRxTx_ISR), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BITCRC), (28 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BITCRC), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BITCRC), (26 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BITCRC), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR), (188 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRx_ISR), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BITCRC), (28 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BITCRC), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR), (348 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseTx_ISR), (8 bytes).
    Removing usbh_pipes.o(.text), (0 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_OpenPipe), (8 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_ClosePipe), (8 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_AllocPipe), (8 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_FreePipe), (8 bytes).
    Removing usbh_core.o(.text), (0 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Init), (8 bytes).
    Removing usbh_core.o(.text.USBH_DeInit), (102 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_DeInit), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_RegisterClass), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_SelectInterface), (8 bytes).
    Removing usbh_core.o(.text.USBH_GetActiveClass), (6 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_GetActiveClass), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_FindInterface), (8 bytes).
    Removing usbh_core.o(.text.USBH_FindInterfaceIndex), (44 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_FindInterfaceIndex), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Start), (8 bytes).
    Removing usbh_core.o(.text.USBH_Stop), (36 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Stop), (8 bytes).
    Removing usbh_core.o(.text.USBH_ReEnumerate), (56 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_ReEnumerate), (8 bytes).
    Removing usbh_core.o(.text.USBH_IsPortEnabled), (6 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_IsPortEnabled), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Process), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_SetTimer), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_IncTimer), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_PortEnabled), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_PortDisabled), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_Connect), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_Disconnect), (8 bytes).
    Removing usbh_ioreq.o(.text), (0 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendSetup), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendData), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_CtlReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_BulkSendData), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_BulkReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_InterruptReceiveData), (38 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_InterruptSendData), (40 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptSendData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_IsocReceiveData), (40 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_IsocReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_IsocSendData), (40 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_IsocSendData), (8 bytes).
    Removing usbh_ctlreq.o(.text), (0 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_DevDesc), (8 bytes).
    Removing usbh_ctlreq.o(.text.USBH_GetDescriptor), (58 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_GetDescriptor), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_CfgDesc), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_StringDesc), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_CtlReq), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetAddress), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetCfg), (8 bytes).
    Removing usbh_ctlreq.o(.text.USBH_SetInterface), (28 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetInterface), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetFeature), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_ClrFeature), (8 bytes).
    Removing usbh_ctlreq.o(.text.USBH_GetNextDesc), (12 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_GetNextDesc), (8 bytes).
    Removing usbh_diskio_dma.o(.text), (0 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_initialize), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_status), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_read), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_write), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_ioctl), (8 bytes).
    Removing usbh_conf.o(.text), (0 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspInit), (8 bytes).
    Removing usbh_conf.o(.text.HAL_HCD_MspDeInit), (28 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspDeInit), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_SOF_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_Connect_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_Init), (8 bytes).
    Removing usbh_conf.o(.text.USBH_LL_DeInit), (14 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_DeInit), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_Start), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_Stop), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetSpeed), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_ResetPort), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetLastXferSize), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_OpenPipe), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_ClosePipe), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_SubmitURB), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetURBState), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_DriverVBUS), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_SetToggle), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetToggle), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_Delay), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.text.__io_putchar), (2 bytes).
    Removing main.o(.ARM.exidx.text.__io_putchar), (8 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.USBH_UserProcess), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.OTG_FS_IRQHandler), (8 bytes).
    Removing usbh_msc_bot.o(.text), (0 bytes).
    Removing usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_Reset), (22 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_Reset), (8 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_GetMaxLUN), (8 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Init), (8 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Process), (8 bytes).
    Removing usbh_msc.o(.text), (0 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceInit), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceDeInit), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_ClassRequest), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_Process), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_SOFProcess), (8 bytes).
    Removing usbh_msc.o(.text.USBH_MSC_IsReady), (26 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_IsReady), (8 bytes).
    Removing usbh_msc.o(.text.USBH_MSC_GetMaxLUN), (26 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetMaxLUN), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_UnitIsReady), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetLUNInfo), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_Read), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_RdWrProcess), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_Write), (8 bytes).
    Removing usbh_msc_scsi.o(.text), (0 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_TestUnitReady), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_ReadCapacity), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Inquiry), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_RequestSense), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Write), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Read), (8 bytes).
    Removing dadd.o(.text), (334 bytes).
    Removing dmul.o(.text), (228 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).
    Removing depilogue.o(.text), (186 bytes).

875 unused section(s) (total 54085 bytes) removed from the image.
