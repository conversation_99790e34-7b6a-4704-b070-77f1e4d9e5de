Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    stm32f401_discovery.o(.ARM.exidx.text.BSP_GetVersion) refers to stm32f401_discovery.o(.text.BSP_GetVersion) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_LED_Init) refers to stm32f401_discovery.o(.rodata.GPIO_PIN) for GPIO_PIN
    stm32f401_discovery.o(.text.BSP_LED_Init) refers to stm32f401_discovery.o(.data.GPIO_PORT) for GPIO_PORT
    stm32f401_discovery.o(.text.BSP_LED_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.BSP_LED_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Init) refers to stm32f401_discovery.o(.text.BSP_LED_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_LED_On) refers to stm32f401_discovery.o(.data.GPIO_PORT) for GPIO_PORT
    stm32f401_discovery.o(.text.BSP_LED_On) refers to stm32f401_discovery.o(.rodata.GPIO_PIN) for GPIO_PIN
    stm32f401_discovery.o(.text.BSP_LED_On) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_On) refers to stm32f401_discovery.o(.text.BSP_LED_On) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_LED_Off) refers to stm32f401_discovery.o(.data.GPIO_PORT) for GPIO_PORT
    stm32f401_discovery.o(.text.BSP_LED_Off) refers to stm32f401_discovery.o(.rodata.GPIO_PIN) for GPIO_PIN
    stm32f401_discovery.o(.text.BSP_LED_Off) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Off) refers to stm32f401_discovery.o(.text.BSP_LED_Off) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_LED_Toggle) refers to stm32f401_discovery.o(.data.GPIO_PORT) for GPIO_PORT
    stm32f401_discovery.o(.text.BSP_LED_Toggle) refers to stm32f401_discovery.o(.rodata.GPIO_PIN) for GPIO_PIN
    stm32f401_discovery.o(.text.BSP_LED_Toggle) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Toggle) refers to stm32f401_discovery.o(.text.BSP_LED_Toggle) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_PB_Init) refers to stm32f401_discovery.o(.data.BUTTON_PORT) for BUTTON_PORT
    stm32f401_discovery.o(.text.BSP_PB_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.BSP_PB_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f401_discovery.o(.text.BSP_PB_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f401_discovery.o(.ARM.exidx.text.BSP_PB_Init) refers to stm32f401_discovery.o(.text.BSP_PB_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.BSP_PB_GetState) refers to stm32f401_discovery.o(.data.BUTTON_PORT) for BUTTON_PORT
    stm32f401_discovery.o(.text.BSP_PB_GetState) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    stm32f401_discovery.o(.ARM.exidx.text.BSP_PB_GetState) refers to stm32f401_discovery.o(.text.BSP_PB_GetState) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.GYRO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.GYRO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.text.GYRO_IO_Init) refers to stm32f401_discovery.o(.text.SPIx_Init) for SPIx_Init
    stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Init) refers to stm32f401_discovery.o(.text.GYRO_IO_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.SPIx_Init) refers to stm32f401_discovery.o(.bss.SpiHandle) for SpiHandle
    stm32f401_discovery.o(.text.SPIx_Init) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_GetState) for HAL_SPI_GetState
    stm32f401_discovery.o(.text.SPIx_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.SPIx_Init) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Init) for HAL_SPI_Init
    stm32f401_discovery.o(.ARM.exidx.text.SPIx_Init) refers to stm32f401_discovery.o(.text.SPIx_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f401_discovery.o(.data.SpixTimeout) for SpixTimeout
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f401_discovery.o(.bss.SpiHandle) for SpiHandle
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit) for HAL_SPI_DeInit
    stm32f401_discovery.o(.text.GYRO_IO_Write) refers to stm32f401_discovery.o(.text.SPIx_Init) for SPIx_Init
    stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Write) refers to stm32f401_discovery.o(.text.GYRO_IO_Write) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f401_discovery.o(.data.SpixTimeout) for SpixTimeout
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f401_discovery.o(.bss.SpiHandle) for SpiHandle
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit) for HAL_SPI_DeInit
    stm32f401_discovery.o(.text.GYRO_IO_Read) refers to stm32f401_discovery.o(.text.SPIx_Init) for SPIx_Init
    stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Read) refers to stm32f401_discovery.o(.text.GYRO_IO_Read) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.AUDIO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.AUDIO_IO_Init) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.text.AUDIO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    stm32f401_discovery.o(.text.AUDIO_IO_Init) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Init) refers to stm32f401_discovery.o(.text.AUDIO_IO_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for HAL_I2C_GetState
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f401_discovery.o(.text.I2Cx_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for HAL_I2C_Init
    stm32f401_discovery.o(.ARM.exidx.text.I2Cx_Init) refers to stm32f401_discovery.o(.text.I2Cx_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_DeInit) refers to stm32f401_discovery.o(.text.AUDIO_IO_DeInit) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f401_discovery.o(.data.I2cxTimeout) for I2cxTimeout
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for HAL_I2C_DeInit
    stm32f401_discovery.o(.text.AUDIO_IO_Write) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Write) refers to stm32f401_discovery.o(.text.AUDIO_IO_Write) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f401_discovery.o(.data.I2cxTimeout) for I2cxTimeout
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for HAL_I2C_DeInit
    stm32f401_discovery.o(.text.AUDIO_IO_Read) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Read) refers to stm32f401_discovery.o(.text.AUDIO_IO_Read) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Init) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Init) refers to stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Init) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_ITConfig) refers to stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f401_discovery.o(.data.I2cxTimeout) for I2cxTimeout
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for HAL_I2C_DeInit
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Write) refers to stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write) for [Anonymous Symbol]
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f401_discovery.o(.data.I2cxTimeout) for I2cxTimeout
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f401_discovery.o(.bss.I2cHandle) for I2cHandle
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for HAL_I2C_Mem_Read
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for HAL_I2C_DeInit
    stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) refers to stm32f401_discovery.o(.text.I2Cx_Init) for I2Cx_Init
    stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Read) refers to stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read) for [Anonymous Symbol]
    diskio.o(.text.disk_status) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_status) refers to diskio.o(.text.disk_status) for [Anonymous Symbol]
    diskio.o(.text.disk_initialize) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_initialize) refers to diskio.o(.text.disk_initialize) for [Anonymous Symbol]
    diskio.o(.text.disk_read) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_read) refers to diskio.o(.text.disk_read) for [Anonymous Symbol]
    diskio.o(.text.disk_write) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_write) refers to diskio.o(.text.disk_write) for [Anonymous Symbol]
    diskio.o(.text.disk_ioctl) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_ioctl) refers to diskio.o(.text.disk_ioctl) for [Anonymous Symbol]
    diskio.o(.ARM.exidx.text.get_fattime) refers to diskio.o(.text.get_fattime) for [Anonymous Symbol]
    ff.o(.text.f_mount) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_mount) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.ARM.exidx.text.f_mount) refers to ff.o(.text.f_mount) for [Anonymous Symbol]
    ff.o(.text.find_volume) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.find_volume) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.find_volume) refers to diskio.o(.text.disk_initialize) for disk_initialize
    ff.o(.text.find_volume) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.find_volume) refers to ff.o(.text.find_volume) for [Anonymous Symbol]
    ff.o(.text.f_open) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_open) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_open) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_open) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_open) refers to ff.o(.text.dir_register) for dir_register
    ff.o(.text.f_open) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_open) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_open) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.f_open) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.f_open) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_open) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.f_open) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.ARM.exidx.text.f_open) refers to ff.o(.text.f_open) for [Anonymous Symbol]
    ff.o(.text.follow_path) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.follow_path) refers to unicode.o(.text.ff_convert) for ff_convert
    ff.o(.text.follow_path) refers to ff.o(.rodata.ExCvt) for ExCvt
    ff.o(.text.follow_path) refers to ff.o(.text.dir_find) for dir_find
    ff.o(.ARM.exidx.text.follow_path) refers to ff.o(.text.follow_path) for [Anonymous Symbol]
    ff.o(.text.dir_register) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_register) refers to ff.o(.text.dir_find) for dir_find
    ff.o(.text.dir_register) refers to ff.o(.text.dir_next) for dir_next
    ff.o(.text.dir_register) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_register) refers to ff.o(.text.dir_sdi) for dir_sdi
    ff.o(.ARM.exidx.text.dir_register) refers to ff.o(.text.dir_register) for [Anonymous Symbol]
    ff.o(.text.remove_chain) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.remove_chain) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.remove_chain) refers to ff.o(.text.remove_chain) for [Anonymous Symbol]
    ff.o(.text.move_window) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.move_window) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.ARM.exidx.text.move_window) refers to ff.o(.text.move_window) for [Anonymous Symbol]
    ff.o(.text.inc_lock) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.ARM.exidx.text.inc_lock) refers to ff.o(.text.inc_lock) for [Anonymous Symbol]
    ff.o(.text.get_fat) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.get_fat) refers to ff.o(.text.get_fat) for [Anonymous Symbol]
    ff.o(.text.f_read) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_read) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_read) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.text.f_read) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_read) refers to ff.o(.text.f_read) for [Anonymous Symbol]
    ff.o(.text.f_write) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_write) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.f_write) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.f_write) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.ARM.exidx.text.f_write) refers to ff.o(.text.f_write) for [Anonymous Symbol]
    ff.o(.text.create_chain) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.create_chain) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.ARM.exidx.text.create_chain) refers to ff.o(.text.create_chain) for [Anonymous Symbol]
    ff.o(.text.f_sync) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_sync) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.f_sync) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_sync) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.f_sync) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.ARM.exidx.text.f_sync) refers to ff.o(.text.f_sync) for [Anonymous Symbol]
    ff.o(.text.sync_fs) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.sync_fs) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.sync_fs) refers to diskio.o(.text.disk_ioctl) for disk_ioctl
    ff.o(.ARM.exidx.text.sync_fs) refers to ff.o(.text.sync_fs) for [Anonymous Symbol]
    ff.o(.text.f_close) refers to ff.o(.text.f_sync) for f_sync
    ff.o(.text.f_close) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_close) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.ARM.exidx.text.f_close) refers to ff.o(.text.f_close) for [Anonymous Symbol]
    ff.o(.text.f_lseek) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_lseek) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_lseek) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.f_lseek) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.text.f_lseek) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_lseek) refers to ff.o(.text.f_lseek) for [Anonymous Symbol]
    ff.o(.text.f_opendir) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_opendir) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_opendir) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_opendir) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_opendir) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_opendir) refers to ff.o(.text.inc_lock) for inc_lock
    ff.o(.ARM.exidx.text.f_opendir) refers to ff.o(.text.f_opendir) for [Anonymous Symbol]
    ff.o(.text.dir_sdi) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.dir_sdi) refers to ff.o(.text.dir_sdi) for [Anonymous Symbol]
    ff.o(.text.f_closedir) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_closedir) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.ARM.exidx.text.f_closedir) refers to ff.o(.text.f_closedir) for [Anonymous Symbol]
    ff.o(.text.f_readdir) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_readdir) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_readdir) refers to ff.o(.text.dir_read) for dir_read
    ff.o(.text.f_readdir) refers to ff.o(.text.get_fileinfo) for get_fileinfo
    ff.o(.text.f_readdir) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_readdir) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.f_readdir) refers to ff.o(.text.f_readdir) for [Anonymous Symbol]
    ff.o(.text.dir_read) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_read) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_read) refers to ff.o(.text.sum_sfn) for sum_sfn
    ff.o(.ARM.exidx.text.dir_read) refers to ff.o(.text.dir_read) for [Anonymous Symbol]
    ff.o(.text.get_fileinfo) refers to unicode.o(.text.ff_convert) for ff_convert
    ff.o(.ARM.exidx.text.get_fileinfo) refers to ff.o(.text.get_fileinfo) for [Anonymous Symbol]
    ff.o(.text.dir_next) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_next) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.dir_next) refers to ff.o(.text.sync_window) for sync_window
    ff.o(.text.dir_next) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.ARM.exidx.text.dir_next) refers to ff.o(.text.dir_next) for [Anonymous Symbol]
    ff.o(.text.f_stat) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_stat) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_stat) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_stat) refers to ff.o(.text.get_fileinfo) for get_fileinfo
    ff.o(.text.f_stat) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.ARM.exidx.text.f_stat) refers to ff.o(.text.f_stat) for [Anonymous Symbol]
    ff.o(.text.f_getfree) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_getfree) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_getfree) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.f_getfree) refers to ff.o(.text.f_getfree) for [Anonymous Symbol]
    ff.o(.text.f_truncate) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_truncate) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_truncate) refers to ff.o(.text.remove_chain) for remove_chain
    ff.o(.text.f_truncate) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_truncate) refers to ff.o(.text.f_truncate) for [Anonymous Symbol]
    ff.o(.text.f_unlink) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_unlink) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_unlink) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_unlink) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_unlink) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_unlink) refers to ff.o(.text.dir_remove) for dir_remove
    ff.o(.text.f_unlink) refers to ff.o(.text.remove_chain) for remove_chain
    ff.o(.text.f_unlink) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.text.f_unlink) refers to ff.o(.text.dir_sdi) for dir_sdi
    ff.o(.text.f_unlink) refers to ff.o(.text.dir_read) for dir_read
    ff.o(.ARM.exidx.text.f_unlink) refers to ff.o(.text.f_unlink) for [Anonymous Symbol]
    ff.o(.text.dir_remove) refers to ff.o(.text.dir_sdi) for dir_sdi
    ff.o(.text.dir_remove) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_remove) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.dir_remove) refers to ff.o(.text.dir_remove) for [Anonymous Symbol]
    ff.o(.text.f_mkdir) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_mkdir) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_mkdir) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_mkdir) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.f_mkdir) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_mkdir) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_mkdir) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_mkdir) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.f_mkdir) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.f_mkdir) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.f_mkdir) refers to ff.o(.text.dir_register) for dir_register
    ff.o(.text.f_mkdir) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.ARM.exidx.text.f_mkdir) refers to ff.o(.text.f_mkdir) for [Anonymous Symbol]
    ff.o(.text.sync_window) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.sync_window) refers to ff.o(.text.sync_window) for [Anonymous Symbol]
    ff.o(.text.f_rename) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_rename) refers to syscall.o(.text.ff_memalloc) for ff_memalloc
    ff.o(.text.f_rename) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_rename) refers to syscall.o(.text.ff_memfree) for ff_memfree
    ff.o(.text.f_rename) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_rename) refers to memcpya.o(.text) for __aeabi_memcpy
    ff.o(.text.f_rename) refers to ff.o(.text.dir_register) for dir_register
    ff.o(.text.f_rename) refers to ff.o(.text.dir_remove) for dir_remove
    ff.o(.text.f_rename) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.text.f_rename) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.f_rename) refers to ff.o(.text.f_rename) for [Anonymous Symbol]
    ff.o(.text.f_mkfs) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_mkfs) refers to diskio.o(.text.disk_initialize) for disk_initialize
    ff.o(.text.f_mkfs) refers to diskio.o(.text.disk_ioctl) for disk_ioctl
    ff.o(.text.f_mkfs) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.f_mkfs) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_mkfs) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_mkfs) refers to ff.o(.text.f_mkfs) for [Anonymous Symbol]
    ff.o(.text.dir_find) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_find) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_find) refers to unicode.o(.text.ff_wtoupper) for ff_wtoupper
    ff.o(.ARM.exidx.text.dir_find) refers to ff.o(.text.dir_find) for [Anonymous Symbol]
    ff.o(.ARM.exidx.text.sum_sfn) refers to ff.o(.text.sum_sfn) for [Anonymous Symbol]
    ff.o(.text.put_fat) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.put_fat) refers to ff.o(.text.put_fat) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_LinkDriverEx) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriverEx) refers to ff_gen_drv.o(.text.FATFS_LinkDriverEx) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_LinkDriver) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriver) refers to ff_gen_drv.o(.text.FATFS_LinkDriver) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_UnLinkDriverEx) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriverEx) refers to ff_gen_drv.o(.text.FATFS_UnLinkDriverEx) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_UnLinkDriver) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriver) refers to ff_gen_drv.o(.text.FATFS_UnLinkDriver) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_GetAttachedDriversNbr) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_GetAttachedDriversNbr) refers to ff_gen_drv.o(.text.FATFS_GetAttachedDriversNbr) for [Anonymous Symbol]
    syscall.o(.text.ff_memalloc) refers to malloc.o(i.malloc) for malloc
    syscall.o(.ARM.exidx.text.ff_memalloc) refers to syscall.o(.text.ff_memalloc) for [Anonymous Symbol]
    syscall.o(.text.ff_memfree) refers to malloc.o(i.free) for free
    syscall.o(.ARM.exidx.text.ff_memfree) refers to syscall.o(.text.ff_memfree) for [Anonymous Symbol]
    unicode.o(.text.ff_convert) refers to unicode.o(.rodata.Tbl) for Tbl
    unicode.o(.ARM.exidx.text.ff_convert) refers to unicode.o(.text.ff_convert) for [Anonymous Symbol]
    unicode.o(.text.ff_wtoupper) refers to unicode.o(.rodata.str2.2) for ff_wtoupper.cvt2
    unicode.o(.ARM.exidx.text.ff_wtoupper) refers to unicode.o(.text.ff_wtoupper) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_CoreInit) refers to stm32f4xx_ll_usb.o(.text.USB_CoreInit) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetTurnaroundTime) refers to stm32f4xx_ll_usb.o(.text.USB_SetTurnaroundTime) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EnableGlobalInt) refers to stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DisableGlobalInt) refers to stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetCurrentMode) refers to stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetMode) refers to stm32f4xx_ll_usb.o(.text.USB_GetMode) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevInit) refers to stm32f4xx_ll_usb.o(.text.USB_DevInit) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_SetDevSpeed) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushTxFifo) refers to stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushRxFifo) refers to stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetDevSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_GetDevSpeed) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateDedicatedEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateDedicatedEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_DeactivateEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateDedicatedEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_DeactivateDedicatedEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStartXfer) refers to stm32f4xx_ll_usb.o(.text.USB_EPStartXfer) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_WritePacket) refers to stm32f4xx_ll_usb.o(.text.USB_WritePacket) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStopXfer) refers to stm32f4xx_ll_usb.o(.text.USB_EPStopXfer) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadPacket) refers to stm32f4xx_ll_usb.o(.text.USB_ReadPacket) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPSetStall) refers to stm32f4xx_ll_usb.o(.text.USB_EPSetStall) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPClearStall) refers to stm32f4xx_ll_usb.o(.text.USB_EPClearStall) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopDevice) refers to stm32f4xx_ll_usb.o(.text.USB_StopDevice) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevAddress) refers to stm32f4xx_ll_usb.o(.text.USB_SetDevAddress) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevConnect) refers to stm32f4xx_ll_usb.o(.text.USB_DevConnect) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevDisconnect) refers to stm32f4xx_ll_usb.o(.text.USB_DevDisconnect) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadInterrupts) refers to stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadChInterrupts) refers to stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllOutEpInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevAllOutEpInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllInEpInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevAllInEpInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevOutEPInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevOutEPInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevInEPInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevInEPInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ClearInterrupts) refers to stm32f4xx_ll_usb.o(.text.USB_ClearInterrupts) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateSetup) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateSetup) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EP0_OutStart) refers to stm32f4xx_ll_usb.o(.text.USB_EP0_OutStart) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HostInit) refers to stm32f4xx_ll_usb.o(.text.USB_HostInit) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_InitFSLSPClkSel) refers to stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.text.USB_ResetPort) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ResetPort) refers to stm32f4xx_ll_usb.o(.text.USB_ResetPort) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DriveVbus) refers to stm32f4xx_ll_usb.o(.text.USB_DriveVbus) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetHostSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetCurrentFrame) refers to stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Init) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Init) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_StartXfer) refers to stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_ReadInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Halt) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Halt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DoPing) refers to stm32f4xx_ll_usb.o(.text.USB_DoPing) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopHost) refers to stm32f4xx_ll_usb.o(.text.USB_StopHost) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateRemoteWakeup) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateRemoteWakeup) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeActivateRemoteWakeup) refers to stm32f4xx_ll_usb.o(.text.USB_DeActivateRemoteWakeup) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch) for [Anonymous Symbol]
    stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches) refers to stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss.pFlash) for pFlash
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBProgram) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBGetConfig) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBGetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_SelectPCROP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_SelectPCROP) for [Anonymous Symbol]
    stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_DeSelectPCROP) refers to stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_DeSelectPCROP) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to usbh_conf.o(.text.HAL_HCD_MspInit) for HAL_HCD_MspInit
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_CoreInit) for USB_CoreInit
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode) for USB_SetCurrentMode
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_HostInit) for USB_HostInit
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for USB_GetHostSpeed
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Init) for USB_HC_Init
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_ClearHubInfo) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_ClearHubInfo) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Halt) for USB_HC_Halt
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Halt) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) refers to usbh_conf.o(.text.HAL_HCD_MspDeInit) for HAL_HCD_MspDeInit
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) refers to stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_DeInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspDeInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest) refers to stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer) for USB_HC_StartXfer
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SubmitRequest) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_GetMode) for USB_GetMode
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts) for USB_ReadInterrupts
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel) for USB_InitFSLSPClkSel
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) for HAL_HCD_Disconnect_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_SOF_Callback) for HAL_HCD_SOF_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt) for USB_HC_ReadInterrupt
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Halt) for USB_HC_Halt
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts) for USB_ReadChInterrupts
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) for HAL_HCD_HC_NotifyURBChange_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_Connect_Callback) for HAL_HCD_Connect_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) for HAL_HCD_PortDisabled_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_ReadPacket) for USB_ReadPacket
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) for HAL_HCD_PortEnabled_Callback
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_IRQHandler) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Disconnect_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_SOF_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_SOF_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_WKUP_IRQHandler) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_WKUP_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Connect_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Connect_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortEnabled_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortDisabled_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) refers to stm32f4xx_ll_usb.o(.text.USB_DriveVbus) for USB_DriveVbus
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) refers to stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt) for USB_EnableGlobalInt
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Start) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop) refers to stm32f4xx_ll_usb.o(.text.USB_StopHost) for USB_StopHost
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Stop) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort) refers to stm32f4xx_ll_usb.o(.text.USB_ResetPort) for USB_ResetPort
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_ResetPort) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetURBState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetXferCount) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame) refers to stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame) for USB_GetCurrentFrame
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentFrame) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for USB_GetHostSpeed
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentSpeed) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SetHubInfo) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for USB_GetHostSpeed
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SetHubInfo) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SetHubInfo) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Init) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_MspInit) for HAL_SPI_MspInit
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Init) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Init) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspInit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_MspDeInit) for HAL_SPI_MspDeInit
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DeInit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspDeInit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_EndRxTxTransaction) refers to stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for HAL_SPI_TransmitReceive
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction) for SPI_EndRxTxTransaction
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_WaitFlagStateUntilTimeout) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_TxISR_16BIT) for SPI_TxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_TxISR_8BIT) for SPI_TxISR_8BIT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_TxISR_16BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) for SPI_CloseTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_TxISR_8BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT) for SPI_RxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT) for SPI_RxISR_8BIT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) for SPI_2linesRxISR_8BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) for SPI_2linesRxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) for SPI_2linesTxISR_16BIT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) for SPI_2linesTxISR_8BIT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BITCRC) for SPI_RxISR_16BITCRC
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BITCRC) for SPI_RxISR_8BITCRC
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BITCRC) for SPI_2linesRxISR_16BITCRC
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_16BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BITCRC) for SPI_2linesRxISR_8BITCRC
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_8BIT) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt) for SPI_DMAHalfTransmitCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) for SPI_DMATransmitCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_DMA) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxHalfCpltCallback) for HAL_SPI_TxHalfCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAError) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAError) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) for HAL_SPI_TransmitReceive_DMA
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_DMA) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) for SPI_DMAHalfReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) for SPI_DMAHalfTransmitReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) for SPI_DMATransmitReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) for SPI_DMAReceiveCplt
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAError) for SPI_DMAError
    stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_DMA) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback) for HAL_SPI_RxHalfCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback) for HAL_SPI_TxRxHalfCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitReceiveCplt) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_AbortTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortTx_ISR) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_AbortRx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback) for SPI_DMATxAbortCallback
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) for SPI_DMARxAbortCallback
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortTx_ISR) for SPI_AbortTx_ISR
    stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR) for SPI_AbortRx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort_IT) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for HAL_SPI_AbortCpltCallback
    stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMARxAbortCallback) refers to stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_AbortCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAPause) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAResume) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAStop) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError) for SPI_DMAAbortOnError
    stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_IRQHandler) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAAbortOnError) refers to stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_ErrorCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxHalfCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxHalfCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxHalfCpltCallback) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetState) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetError) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BITCRC) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback) for HAL_SPI_TxRxCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRxTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR) for SPI_CloseRxTx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BITCRC) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BITCRC) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback) for HAL_SPI_RxCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR) for SPI_CloseRx_ISR
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BITCRC) refers to stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BITCRC) for [Anonymous Symbol]
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout) for SPI_WaitFlagStateUntilTimeout
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback) for HAL_SPI_ErrorCallback
    stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback) for HAL_SPI_TxCpltCallback
    stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseTx_ISR) refers to stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR) for [Anonymous Symbol]
    usbh_pipes.o(.text.USBH_OpenPipe) refers to usbh_conf.o(.text.USBH_LL_OpenPipe) for USBH_LL_OpenPipe
    usbh_pipes.o(.ARM.exidx.text.USBH_OpenPipe) refers to usbh_pipes.o(.text.USBH_OpenPipe) for [Anonymous Symbol]
    usbh_pipes.o(.text.USBH_ClosePipe) refers to usbh_conf.o(.text.USBH_LL_ClosePipe) for USBH_LL_ClosePipe
    usbh_pipes.o(.ARM.exidx.text.USBH_ClosePipe) refers to usbh_pipes.o(.text.USBH_ClosePipe) for [Anonymous Symbol]
    usbh_pipes.o(.ARM.exidx.text.USBH_AllocPipe) refers to usbh_pipes.o(.text.USBH_AllocPipe) for [Anonymous Symbol]
    usbh_pipes.o(.ARM.exidx.text.USBH_FreePipe) refers to usbh_pipes.o(.text.USBH_FreePipe) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Init) refers to memseta.o(.text) for __aeabi_memclr4
    usbh_core.o(.text.USBH_Init) refers to usbh_conf.o(.text.USBH_LL_Init) for USBH_LL_Init
    usbh_core.o(.ARM.exidx.text.USBH_Init) refers to usbh_core.o(.text.USBH_Init) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_DeInit) refers to memseta.o(.text) for __aeabi_memclr4
    usbh_core.o(.text.USBH_DeInit) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.ARM.exidx.text.USBH_DeInit) refers to usbh_core.o(.text.USBH_DeInit) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_RegisterClass) refers to usbh_core.o(.text.USBH_RegisterClass) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_SelectInterface) refers to usbh_core.o(.text.USBH_SelectInterface) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_GetActiveClass) refers to usbh_core.o(.text.USBH_GetActiveClass) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_FindInterface) refers to usbh_core.o(.text.USBH_FindInterface) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_FindInterfaceIndex) refers to usbh_core.o(.text.USBH_FindInterfaceIndex) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Start) refers to usbh_conf.o(.text.USBH_LL_Start) for USBH_LL_Start
    usbh_core.o(.text.USBH_Start) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.ARM.exidx.text.USBH_Start) refers to usbh_core.o(.text.USBH_Start) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Stop) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.text.USBH_Stop) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.text.USBH_Stop) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_Stop) refers to usbh_core.o(.text.USBH_Stop) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_ReEnumerate) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.text.USBH_ReEnumerate) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.text.USBH_ReEnumerate) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_ReEnumerate) refers to usbh_core.o(.text.USBH_ReEnumerate) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_IsPortEnabled) refers to usbh_core.o(.text.USBH_IsPortEnabled) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_Delay) for USBH_Delay
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_ResetPort) for USBH_LL_ResetPort
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_Get_DevDesc) for USBH_Get_DevDesc
    usbh_core.o(.text.USBH_Process) refers to usbh_pipes.o(.text.USBH_OpenPipe) for USBH_OpenPipe
    usbh_core.o(.text.USBH_Process) refers to memseta.o(.text) for __aeabi_memclr4
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_Start) for USBH_LL_Start
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_GetSpeed) for USBH_LL_GetSpeed
    usbh_core.o(.text.USBH_Process) refers to usbh_pipes.o(.text.USBH_AllocPipe) for USBH_AllocPipe
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_SetCfg) for USBH_SetCfg
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_SetFeature) for USBH_SetFeature
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_Get_CfgDesc) for USBH_Get_CfgDesc
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_SetAddress) for USBH_SetAddress
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_Get_StringDesc) for USBH_Get_StringDesc
    usbh_core.o(.text.USBH_Process) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_Process) refers to usbh_core.o(.text.USBH_Process) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_SetTimer) refers to usbh_core.o(.text.USBH_LL_SetTimer) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_IncTimer) refers to usbh_core.o(.text.USBH_LL_IncTimer) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_PortEnabled) refers to usbh_core.o(.text.USBH_LL_PortEnabled) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_PortDisabled) refers to usbh_core.o(.text.USBH_LL_PortDisabled) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_Connect) refers to usbh_core.o(.text.USBH_LL_Connect) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_LL_Disconnect) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.text.USBH_LL_Disconnect) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_LL_Disconnect) refers to usbh_core.o(.text.USBH_LL_Disconnect) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_CtlSendSetup) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendSetup) refers to usbh_ioreq.o(.text.USBH_CtlSendSetup) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_CtlSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendData) refers to usbh_ioreq.o(.text.USBH_CtlSendData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_CtlReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_CtlReceiveData) refers to usbh_ioreq.o(.text.USBH_CtlReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_BulkSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_BulkSendData) refers to usbh_ioreq.o(.text.USBH_BulkSendData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_BulkReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_BulkReceiveData) refers to usbh_ioreq.o(.text.USBH_BulkReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_InterruptReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptReceiveData) refers to usbh_ioreq.o(.text.USBH_InterruptReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_InterruptSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptSendData) refers to usbh_ioreq.o(.text.USBH_InterruptSendData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_IsocReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_IsocReceiveData) refers to usbh_ioreq.o(.text.USBH_IsocReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_IsocSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_IsocSendData) refers to usbh_ioreq.o(.text.USBH_IsocSendData) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_Get_DevDesc) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_DevDesc) refers to usbh_ctlreq.o(.text.USBH_Get_DevDesc) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_GetDescriptor) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_GetDescriptor) refers to usbh_ctlreq.o(.text.USBH_GetDescriptor) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_Get_CfgDesc) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_CfgDesc) refers to usbh_ctlreq.o(.text.USBH_Get_CfgDesc) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_Get_StringDesc) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_StringDesc) refers to usbh_ctlreq.o(.text.USBH_Get_StringDesc) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_ioreq.o(.text.USBH_CtlSendSetup) for USBH_CtlSendSetup
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_ioreq.o(.text.USBH_CtlSendData) for USBH_CtlSendData
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_conf.o(.text.USBH_LL_GetURBState) for USBH_LL_GetURBState
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_ioreq.o(.text.USBH_CtlReceiveData) for USBH_CtlReceiveData
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_ctlreq.o(.ARM.exidx.text.USBH_CtlReq) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetAddress) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetAddress) refers to usbh_ctlreq.o(.text.USBH_SetAddress) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetCfg) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetCfg) refers to usbh_ctlreq.o(.text.USBH_SetCfg) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetInterface) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetInterface) refers to usbh_ctlreq.o(.text.USBH_SetInterface) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetFeature) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetFeature) refers to usbh_ctlreq.o(.text.USBH_SetFeature) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_ClrFeature) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_ClrFeature) refers to usbh_ctlreq.o(.text.USBH_ClrFeature) for [Anonymous Symbol]
    usbh_ctlreq.o(.ARM.exidx.text.USBH_GetNextDesc) refers to usbh_ctlreq.o(.text.USBH_GetNextDesc) for [Anonymous Symbol]
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_initialize) refers to usbh_diskio_dma.o(.text.USBH_initialize) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_status) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    usbh_diskio_dma.o(.text.USBH_status) refers to usbh_msc.o(.text.USBH_MSC_UnitIsReady) for USBH_MSC_UnitIsReady
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_status) refers to usbh_diskio_dma.o(.text.USBH_status) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_read) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    usbh_diskio_dma.o(.text.USBH_read) refers to usbh_diskio_dma.o(.bss.scratch) for scratch
    usbh_diskio_dma.o(.text.USBH_read) refers to usbh_msc.o(.text.USBH_MSC_Read) for USBH_MSC_Read
    usbh_diskio_dma.o(.text.USBH_read) refers to memcpya.o(.text) for __aeabi_memcpy
    usbh_diskio_dma.o(.text.USBH_read) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for USBH_MSC_GetLUNInfo
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_read) refers to usbh_diskio_dma.o(.text.USBH_read) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_write) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    usbh_diskio_dma.o(.text.USBH_write) refers to usbh_diskio_dma.o(.bss.scratch) for scratch
    usbh_diskio_dma.o(.text.USBH_write) refers to memcpya.o(.text) for __aeabi_memcpy
    usbh_diskio_dma.o(.text.USBH_write) refers to usbh_msc.o(.text.USBH_MSC_Write) for USBH_MSC_Write
    usbh_diskio_dma.o(.text.USBH_write) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for USBH_MSC_GetLUNInfo
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_write) refers to usbh_diskio_dma.o(.text.USBH_write) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_ioctl) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    usbh_diskio_dma.o(.text.USBH_ioctl) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for USBH_MSC_GetLUNInfo
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_ioctl) refers to usbh_diskio_dma.o(.text.USBH_ioctl) for [Anonymous Symbol]
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_initialize) for USBH_initialize
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_status) for USBH_status
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_read) for USBH_read
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_write) for USBH_write
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_ioctl) for USBH_ioctl
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspInit) refers to usbh_conf.o(.text.HAL_HCD_MspInit) for [Anonymous Symbol]
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspDeInit) refers to usbh_conf.o(.text.HAL_HCD_MspDeInit) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_SOF_Callback) refers to usbh_core.o(.text.USBH_LL_IncTimer) for USBH_LL_IncTimer
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_SOF_Callback) refers to usbh_conf.o(.text.HAL_HCD_SOF_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_Connect_Callback) refers to usbh_core.o(.text.USBH_LL_Connect) for USBH_LL_Connect
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_Connect_Callback) refers to usbh_conf.o(.text.HAL_HCD_Connect_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) refers to usbh_core.o(.text.USBH_LL_Disconnect) for USBH_LL_Disconnect
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback) refers to usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) refers to usbh_core.o(.text.USBH_LL_PortEnabled) for USBH_LL_PortEnabled
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback) refers to usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) refers to usbh_core.o(.text.USBH_LL_PortDisabled) for USBH_LL_PortDisabled
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback) refers to usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) for [Anonymous Symbol]
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback) refers to usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_Init) refers to usbh_conf.o(.bss.hhcd) for hhcd
    usbh_conf.o(.text.USBH_LL_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) for HAL_HCD_Init
    usbh_conf.o(.text.USBH_LL_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame) for HAL_HCD_GetCurrentFrame
    usbh_conf.o(.text.USBH_LL_Init) refers to usbh_core.o(.text.USBH_LL_SetTimer) for USBH_LL_SetTimer
    usbh_conf.o(.ARM.exidx.text.USBH_LL_Init) refers to usbh_conf.o(.text.USBH_LL_Init) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_DeInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) for HAL_HCD_DeInit
    usbh_conf.o(.ARM.exidx.text.USBH_LL_DeInit) refers to usbh_conf.o(.text.USBH_LL_DeInit) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_Start) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) for HAL_HCD_Start
    usbh_conf.o(.ARM.exidx.text.USBH_LL_Start) refers to usbh_conf.o(.text.USBH_LL_Start) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_Stop) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop) for HAL_HCD_Stop
    usbh_conf.o(.ARM.exidx.text.USBH_LL_Stop) refers to usbh_conf.o(.text.USBH_LL_Stop) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetSpeed) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed) for HAL_HCD_GetCurrentSpeed
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetSpeed) refers to usbh_conf.o(.text.USBH_LL_GetSpeed) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_ResetPort) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort) for HAL_HCD_ResetPort
    usbh_conf.o(.ARM.exidx.text.USBH_LL_ResetPort) refers to usbh_conf.o(.text.USBH_LL_ResetPort) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetLastXferSize) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount) for HAL_HCD_HC_GetXferCount
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetLastXferSize) refers to usbh_conf.o(.text.USBH_LL_GetLastXferSize) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_OpenPipe) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) for HAL_HCD_HC_Init
    usbh_conf.o(.ARM.exidx.text.USBH_LL_OpenPipe) refers to usbh_conf.o(.text.USBH_LL_OpenPipe) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_ClosePipe) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt) for HAL_HCD_HC_Halt
    usbh_conf.o(.ARM.exidx.text.USBH_LL_ClosePipe) refers to usbh_conf.o(.text.USBH_LL_ClosePipe) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_SubmitURB) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest) for HAL_HCD_HC_SubmitRequest
    usbh_conf.o(.ARM.exidx.text.USBH_LL_SubmitURB) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetURBState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState) for HAL_HCD_HC_GetURBState
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetURBState) refers to usbh_conf.o(.text.USBH_LL_GetURBState) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_DriverVBUS) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    usbh_conf.o(.text.USBH_LL_DriverVBUS) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    usbh_conf.o(.ARM.exidx.text.USBH_LL_DriverVBUS) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_SetToggle) refers to usbh_conf.o(.bss.hhcd) for hhcd
    usbh_conf.o(.ARM.exidx.text.USBH_LL_SetToggle) refers to usbh_conf.o(.text.USBH_LL_SetToggle) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetToggle) refers to usbh_conf.o(.bss.hhcd) for hhcd
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetToggle) refers to usbh_conf.o(.text.USBH_LL_GetToggle) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    usbh_conf.o(.ARM.exidx.text.USBH_Delay) refers to usbh_conf.o(.text.USBH_Delay) for [Anonymous Symbol]
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to stm32f401_discovery.o(.text.BSP_LED_Init) for BSP_LED_Init
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.main) refers to main.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    main.o(.text.main) refers to usbh_diskio_dma.o(.rodata.USBH_Driver) for USBH_Driver
    main.o(.text.main) refers to ff_gen_drv.o(.text.FATFS_LinkDriver) for FATFS_LinkDriver
    main.o(.text.main) refers to main.o(.bss.hUSB_Host) for hUSB_Host
    main.o(.text.main) refers to main.o(.text.USBH_UserProcess) for USBH_UserProcess
    main.o(.text.main) refers to usbh_core.o(.text.USBH_Init) for USBH_Init
    main.o(.text.main) refers to usbh_msc.o(.data.USBH_msc) for USBH_msc
    main.o(.text.main) refers to usbh_core.o(.text.USBH_RegisterClass) for USBH_RegisterClass
    main.o(.text.main) refers to usbh_core.o(.text.USBH_Start) for USBH_Start
    main.o(.text.main) refers to main.o(.bss.MyFile) for MyFile
    main.o(.text.main) refers to usbh_core.o(.text.USBH_Process) for USBH_Process
    main.o(.text.main) refers to main.o(.bss.USBDISKFatFs) for USBDISKFatFs
    main.o(.text.main) refers to ff.o(.text.f_mount) for f_mount
    main.o(.text.main) refers to main.o(.rodata.str1.1) for .L.str
    main.o(.text.main) refers to ff.o(.text.f_open) for f_open
    main.o(.text.main) refers to ff.o(.text.f_write) for f_write
    main.o(.text.main) refers to ff.o(.text.f_close) for f_close
    main.o(.text.main) refers to ff.o(.text.f_read) for f_read
    main.o(.text.main) refers to stm32f401_discovery.o(.text.BSP_LED_On) for BSP_LED_On
    main.o(.text.main) refers to ff_gen_drv.o(.text.FATFS_UnLinkDriver) for FATFS_UnLinkDriver
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.USBH_UserProcess) refers to main.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    main.o(.text.USBH_UserProcess) refers to stm32f401_discovery.o(.text.BSP_LED_Off) for BSP_LED_Off
    main.o(.text.USBH_UserProcess) refers to main.o(.rodata.str1.1) for .L.str.1
    main.o(.text.USBH_UserProcess) refers to ff.o(.text.f_mount) for f_mount
    main.o(.ARM.exidx.text.USBH_UserProcess) refers to main.o(.text.USBH_UserProcess) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.OTG_FS_IRQHandler) refers to usbh_conf.o(.bss.hhcd) for hhcd
    stm32f4xx_it.o(.text.OTG_FS_IRQHandler) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) for HAL_HCD_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.OTG_FS_IRQHandler) refers to stm32f4xx_it.o(.text.OTG_FS_IRQHandler) for [Anonymous Symbol]
    usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_Reset) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_Reset) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_Reset) for [Anonymous Symbol]
    usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_GetMaxLUN) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN) for [Anonymous Symbol]
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Init) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Init) for [Anonymous Symbol]
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ioreq.o(.text.USBH_BulkSendData) for USBH_BulkSendData
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ctlreq.o(.text.USBH_ClrFeature) for USBH_ClrFeature
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_GetToggle) for USBH_LL_GetToggle
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_SetToggle) for USBH_LL_SetToggle
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ioreq.o(.text.USBH_BulkReceiveData) for USBH_BulkReceiveData
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_GetURBState) for USBH_LL_GetURBState
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_GetLastXferSize) for USBH_LL_GetLastXferSize
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Process) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_core.o(.text.USBH_FindInterface) for USBH_FindInterface
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_core.o(.text.USBH_SelectInterface) for USBH_SelectInterface
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to calloc.o(.text) for calloc
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_pipes.o(.text.USBH_AllocPipe) for USBH_AllocPipe
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Init) for USBH_MSC_BOT_Init
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_pipes.o(.text.USBH_OpenPipe) for USBH_OpenPipe
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_conf.o(.text.USBH_LL_SetToggle) for USBH_LL_SetToggle
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceInit) refers to usbh_msc.o(.text.USBH_MSC_InterfaceInit) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) refers to usbh_pipes.o(.text.USBH_ClosePipe) for USBH_ClosePipe
    usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) refers to malloc.o(i.free) for free
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceDeInit) refers to usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_ClassRequest) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN) for USBH_MSC_BOT_REQ_GetMaxLUN
    usbh_msc.o(.text.USBH_MSC_ClassRequest) refers to usbh_ctlreq.o(.text.USBH_ClrFeature) for USBH_ClrFeature
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_ClassRequest) refers to usbh_msc.o(.text.USBH_MSC_ClassRequest) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity) for USBH_MSC_SCSI_ReadCapacity
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady) for USBH_MSC_SCSI_TestUnitReady
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) for USBH_MSC_SCSI_Inquiry
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) for USBH_MSC_SCSI_RequestSense
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_Process) refers to usbh_msc.o(.text.USBH_MSC_Process) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_SOFProcess) refers to usbh_msc.o(.text.USBH_MSC_SOFProcess) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_IsReady) refers to usbh_msc.o(.text.USBH_MSC_IsReady) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetMaxLUN) refers to usbh_msc.o(.text.USBH_MSC_GetMaxLUN) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_UnitIsReady) refers to usbh_msc.o(.text.USBH_MSC_UnitIsReady) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetLUNInfo) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_Read) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) for USBH_MSC_SCSI_Read
    usbh_msc.o(.text.USBH_MSC_Read) refers to usbh_msc.o(.text.USBH_MSC_RdWrProcess) for USBH_MSC_RdWrProcess
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_Read) refers to usbh_msc.o(.text.USBH_MSC_Read) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_RdWrProcess) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) for USBH_MSC_SCSI_Read
    usbh_msc.o(.text.USBH_MSC_RdWrProcess) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) for USBH_MSC_SCSI_Write
    usbh_msc.o(.text.USBH_MSC_RdWrProcess) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) for USBH_MSC_SCSI_RequestSense
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_RdWrProcess) refers to usbh_msc.o(.text.USBH_MSC_RdWrProcess) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_Write) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) for USBH_MSC_SCSI_Write
    usbh_msc.o(.text.USBH_MSC_Write) refers to usbh_msc.o(.text.USBH_MSC_RdWrProcess) for USBH_MSC_RdWrProcess
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_Write) refers to usbh_msc.o(.text.USBH_MSC_Write) for [Anonymous Symbol]
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.rodata.str1.1) for [Anonymous Symbol]
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_InterfaceInit) for USBH_MSC_InterfaceInit
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) for USBH_MSC_InterfaceDeInit
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_ClassRequest) for USBH_MSC_ClassRequest
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_Process) for USBH_MSC_Process
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_SOFProcess) for USBH_MSC_SOFProcess
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_TestUnitReady) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_ReadCapacity) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) refers to memseta.o(.text) for __aeabi_memclr
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Inquiry) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_RequestSense) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Write) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Read) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) for [Anonymous Symbol]
    startup_stm32f401xc.o(RESET) refers to startup_stm32f401xc.o(STACK) for __initial_sp
    startup_stm32f401xc.o(RESET) refers to startup_stm32f401xc.o(.text) for Reset_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f401xc.o(RESET) refers to stm32f4xx_it.o(.text.OTG_FS_IRQHandler) for OTG_FS_IRQHandler
    startup_stm32f401xc.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f401xc.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f401xc.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f401xc.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f401xc.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f401xc.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    calloc.o(.text) refers to malloc.o(i.malloc) for malloc
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f401xc.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f401xc.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_stm32f4xx.o(.rodata.APBPrescTable), (8 bytes).
    Removing stm32f401_discovery.o(.text), (0 bytes).
    Removing stm32f401_discovery.o(.text.BSP_GetVersion), (10 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_GetVersion), (8 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Init), (8 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_On), (8 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Off), (8 bytes).
    Removing stm32f401_discovery.o(.text.BSP_LED_Toggle), (30 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_LED_Toggle), (8 bytes).
    Removing stm32f401_discovery.o(.text.BSP_PB_Init), (134 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_PB_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.BSP_PB_GetState), (22 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.BSP_PB_GetState), (8 bytes).
    Removing stm32f401_discovery.o(.text.GYRO_IO_Init), (122 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.SPIx_Init), (156 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.SPIx_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.GYRO_IO_Write), (206 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Write), (8 bytes).
    Removing stm32f401_discovery.o(.text.GYRO_IO_Read), (206 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.GYRO_IO_Read), (8 bytes).
    Removing stm32f401_discovery.o(.text.AUDIO_IO_Init), (104 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.I2Cx_Init), (202 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.I2Cx_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.AUDIO_IO_DeInit), (2 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_DeInit), (8 bytes).
    Removing stm32f401_discovery.o(.text.AUDIO_IO_Write), (76 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Write), (8 bytes).
    Removing stm32f401_discovery.o(.text.AUDIO_IO_Read), (80 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.AUDIO_IO_Read), (8 bytes).
    Removing stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Init), (68 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Init), (8 bytes).
    Removing stm32f401_discovery.o(.text.COMPASSACCELERO_IO_ITConfig), (84 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_ITConfig), (8 bytes).
    Removing stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Write), (76 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Write), (8 bytes).
    Removing stm32f401_discovery.o(.text.COMPASSACCELERO_IO_Read), (80 bytes).
    Removing stm32f401_discovery.o(.ARM.exidx.text.COMPASSACCELERO_IO_Read), (8 bytes).
    Removing stm32f401_discovery.o(.data.BUTTON_PORT), (4 bytes).
    Removing stm32f401_discovery.o(.rodata.BUTTON_PIN), (2 bytes).
    Removing stm32f401_discovery.o(.rodata.BUTTON_IRQn), (1 bytes).
    Removing stm32f401_discovery.o(.data.I2cxTimeout), (4 bytes).
    Removing stm32f401_discovery.o(.data.SpixTimeout), (4 bytes).
    Removing stm32f401_discovery.o(.bss.SpiHandle), (88 bytes).
    Removing stm32f401_discovery.o(.bss.I2cHandle), (84 bytes).
    Removing diskio.o(.text), (0 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_status), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_initialize), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_read), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_write), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_ioctl), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.get_fattime), (8 bytes).
    Removing ff.o(.text), (0 bytes).
    Removing ff.o(.ARM.exidx.text.f_mount), (8 bytes).
    Removing ff.o(.ARM.exidx.text.find_volume), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_open), (8 bytes).
    Removing ff.o(.ARM.exidx.text.follow_path), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_register), (8 bytes).
    Removing ff.o(.text.remove_chain), (160 bytes).
    Removing ff.o(.ARM.exidx.text.remove_chain), (8 bytes).
    Removing ff.o(.ARM.exidx.text.move_window), (8 bytes).
    Removing ff.o(.text.inc_lock), (162 bytes).
    Removing ff.o(.ARM.exidx.text.inc_lock), (8 bytes).
    Removing ff.o(.ARM.exidx.text.get_fat), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_read), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_write), (8 bytes).
    Removing ff.o(.ARM.exidx.text.create_chain), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_sync), (8 bytes).
    Removing ff.o(.ARM.exidx.text.sync_fs), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_close), (8 bytes).
    Removing ff.o(.text.f_lseek), (740 bytes).
    Removing ff.o(.ARM.exidx.text.f_lseek), (8 bytes).
    Removing ff.o(.text.f_opendir), (310 bytes).
    Removing ff.o(.ARM.exidx.text.f_opendir), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_sdi), (8 bytes).
    Removing ff.o(.text.f_closedir), (104 bytes).
    Removing ff.o(.ARM.exidx.text.f_closedir), (8 bytes).
    Removing ff.o(.text.f_readdir), (408 bytes).
    Removing ff.o(.ARM.exidx.text.f_readdir), (8 bytes).
    Removing ff.o(.text.dir_read), (832 bytes).
    Removing ff.o(.ARM.exidx.text.dir_read), (8 bytes).
    Removing ff.o(.text.get_fileinfo), (490 bytes).
    Removing ff.o(.ARM.exidx.text.get_fileinfo), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_next), (8 bytes).
    Removing ff.o(.text.f_stat), (96 bytes).
    Removing ff.o(.ARM.exidx.text.f_stat), (8 bytes).
    Removing ff.o(.text.f_getfree), (264 bytes).
    Removing ff.o(.ARM.exidx.text.f_getfree), (8 bytes).
    Removing ff.o(.text.f_truncate), (212 bytes).
    Removing ff.o(.ARM.exidx.text.f_truncate), (8 bytes).
    Removing ff.o(.text.f_unlink), (272 bytes).
    Removing ff.o(.ARM.exidx.text.f_unlink), (8 bytes).
    Removing ff.o(.text.dir_remove), (244 bytes).
    Removing ff.o(.ARM.exidx.text.dir_remove), (8 bytes).
    Removing ff.o(.text.f_mkdir), (730 bytes).
    Removing ff.o(.ARM.exidx.text.f_mkdir), (8 bytes).
    Removing ff.o(.ARM.exidx.text.sync_window), (8 bytes).
    Removing ff.o(.text.f_rename), (594 bytes).
    Removing ff.o(.ARM.exidx.text.f_rename), (8 bytes).
    Removing ff.o(.text.f_mkfs), (2016 bytes).
    Removing ff.o(.ARM.exidx.text.f_mkfs), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_find), (8 bytes).
    Removing ff.o(.text.sum_sfn), (130 bytes).
    Removing ff.o(.ARM.exidx.text.sum_sfn), (8 bytes).
    Removing ff.o(.ARM.exidx.text.put_fat), (8 bytes).
    Removing ff_gen_drv.o(.text), (0 bytes).
    Removing ff_gen_drv.o(.text.FATFS_LinkDriverEx), (80 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriverEx), (8 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriver), (8 bytes).
    Removing ff_gen_drv.o(.text.FATFS_UnLinkDriverEx), (48 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriverEx), (8 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriver), (8 bytes).
    Removing ff_gen_drv.o(.text.FATFS_GetAttachedDriversNbr), (12 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_GetAttachedDriversNbr), (8 bytes).
    Removing syscall.o(.text), (0 bytes).
    Removing syscall.o(.ARM.exidx.text.ff_memalloc), (8 bytes).
    Removing syscall.o(.ARM.exidx.text.ff_memfree), (8 bytes).
    Removing unicode.o(.text), (0 bytes).
    Removing unicode.o(.ARM.exidx.text.ff_convert), (8 bytes).
    Removing unicode.o(.ARM.exidx.text.ff_wtoupper), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1236 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (466 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text), (0 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_CoreInit), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_SetTurnaroundTime), (280 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetTurnaroundTime), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EnableGlobalInt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DisableGlobalInt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetCurrentMode), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetMode), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DevInit), (1206 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevInit), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_SetDevSpeed), (16 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevSpeed), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushTxFifo), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushRxFifo), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_GetDevSpeed), (26 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetDevSpeed), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateEndpoint), (130 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateDedicatedEndpoint), (116 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateDedicatedEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DeactivateEndpoint), (130 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DeactivateDedicatedEndpoint), (100 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateDedicatedEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPStartXfer), (570 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStartXfer), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_WritePacket), (106 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_WritePacket), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPStopXfer), (270 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStopXfer), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadPacket), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPSetStall), (62 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPSetStall), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPClearStall), (82 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPClearStall), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_StopDevice), (510 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopDevice), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_SetDevAddress), (34 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevAddress), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DevConnect), (30 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevConnect), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DevDisconnect), (30 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevDisconnect), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadChInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevAllOutEpInterrupt), (14 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllOutEpInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevAllInEpInterrupt), (14 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllInEpInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevOutEPInterrupt), (16 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevOutEPInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevInEPInterrupt), (36 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevInEPInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ClearInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ClearInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateSetup), (32 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateSetup), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EP0_OutStart), (90 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EP0_OutStart), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HostInit), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_InitFSLSPClkSel), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ResetPort), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DriveVbus), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetHostSpeed), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetCurrentFrame), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Init), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_StartXfer), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_ReadInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Halt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DoPing), (32 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DoPing), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopHost), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateRemoteWakeup), (24 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateRemoteWakeup), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DeActivateRemoteWakeup), (18 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeActivateRemoteWakeup), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq), (38 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq), (38 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program), (218 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.FLASH_WaitForLastOperation), (274 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Program_IT), (240 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Program_IT), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_IRQHandler), (340 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OperationErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_EndOfOperationCallback), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Unlock), (46 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Unlock), (42 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Unlock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Lock), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_OB_Launch), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.text.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(.ARM.exidx.text.HAL_FLASH_GetError), (8 bytes).
    Removing stm32f4xx_hal_flash.o(.bss.pFlash), (32 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Init), (354 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT), (162 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort), (142 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT), (36 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler), (452 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (50 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.rodata.cst8), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase), (394 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_Erase_Sector), (70 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_Erase_Sector), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.FLASH_FlushCaches), (88 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.FLASH_FlushCaches), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_Erase_IT), (176 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_Erase_IT), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBProgram), (184 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OBGetConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBProgram), (78 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBProgram), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_AdvOBGetConfig), (14 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_AdvOBGetConfig), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_SelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_SelectPCROP), (8 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.text.HAL_FLASHEx_OB_DeSelectPCROP), (20 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.ARM.exidx.text.HAL_FLASHEx_OB_DeSelectPCROP), (8 bytes).
    Removing stm32f4xx_hal_dcmi.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (70 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init), (356 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit), (52 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (476 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout), (458 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (796 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (378 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAError), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite), (200 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read), (600 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (282 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (298 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (514 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (576 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady), (532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (324 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (598 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort), (286 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (448 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (88 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_ITError), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler), (1540 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE), (178 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF), (190 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE), (318 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (214 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text), (0 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Init), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspInit), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspInit), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Init), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_ClearHubInfo), (18 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_ClearHubInfo), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Halt), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit), (36 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_DeInit), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SubmitRequest), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_Disconnect_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_SOF_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_SOF_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_WKUP_IRQHandler), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_WKUP_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_Connect_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Connect_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortEnabled_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortDisabled_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_NotifyURBChange_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Start), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Stop), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_ResetPort), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetState), (6 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetState), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetURBState), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetXferCount), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetState), (14 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetState), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentFrame), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentSpeed), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SetHubInfo), (64 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SetHubInfo), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text), (0 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Init), (194 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Init), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspInit), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_DeInit), (46 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DeInit), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit), (692 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_EndRxTxTransaction), (184 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_EndRxTxTransaction), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive), (682 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive), (706 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_WaitFlagStateUntilTimeout), (292 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_WaitFlagStateUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_IT), (196 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_TxISR_16BIT), (50 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_16BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_TxISR_8BIT), (54 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_TxISR_8BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_IT), (388 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_IT), (236 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BIT), (76 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BIT), (68 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_16BIT), (72 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_16BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BIT), (70 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesTxISR_8BIT), (76 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesTxISR_8BIT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Transmit_DMA), (246 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitCplt), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMATransmitCplt), (268 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAError), (30 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAError), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Receive_DMA), (282 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TransmitReceive_DMA), (332 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TransmitReceive_DMA), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAHalfReceiveCplt), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAReceiveCplt), (236 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAHalfTransmitReceiveCplt), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAHalfTransmitReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMATransmitReceiveCplt), (316 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATransmitReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort), (480 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_AbortTx_ISR), (26 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_AbortTx_ISR), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_AbortRx_ISR), (146 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_AbortRx_ISR), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_Abort_IT), (458 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMATxAbortCallback), (184 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMARxAbortCallback), (272 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAPause), (32 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAResume), (32 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_DMAStop), (74 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_IRQHandler), (302 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_DMAAbortOnError), (12 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_TxRxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_TxRxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_GetState), (6 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetState), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.HAL_SPI_GetError), (4 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.HAL_SPI_GetError), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_8BITCRC), (42 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_8BITCRC), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_CloseRxTx_ISR), (422 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRxTx_ISR), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_2linesRxISR_16BITCRC), (28 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_2linesRxISR_16BITCRC), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_RxISR_8BITCRC), (26 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_8BITCRC), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_CloseRx_ISR), (188 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseRx_ISR), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_RxISR_16BITCRC), (28 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_RxISR_16BITCRC), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text.SPI_CloseTx_ISR), (348 bytes).
    Removing stm32f4xx_hal_spi.o(.ARM.exidx.text.SPI_CloseTx_ISR), (8 bytes).
    Removing usbh_pipes.o(.text), (0 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_OpenPipe), (8 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_ClosePipe), (8 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_AllocPipe), (8 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_FreePipe), (8 bytes).
    Removing usbh_core.o(.text), (0 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Init), (8 bytes).
    Removing usbh_core.o(.text.USBH_DeInit), (102 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_DeInit), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_RegisterClass), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_SelectInterface), (8 bytes).
    Removing usbh_core.o(.text.USBH_GetActiveClass), (6 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_GetActiveClass), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_FindInterface), (8 bytes).
    Removing usbh_core.o(.text.USBH_FindInterfaceIndex), (44 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_FindInterfaceIndex), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Start), (8 bytes).
    Removing usbh_core.o(.text.USBH_Stop), (36 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Stop), (8 bytes).
    Removing usbh_core.o(.text.USBH_ReEnumerate), (56 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_ReEnumerate), (8 bytes).
    Removing usbh_core.o(.text.USBH_IsPortEnabled), (6 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_IsPortEnabled), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Process), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_SetTimer), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_IncTimer), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_PortEnabled), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_PortDisabled), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_Connect), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_Disconnect), (8 bytes).
    Removing usbh_ioreq.o(.text), (0 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendSetup), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendData), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_CtlReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_BulkSendData), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_BulkReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_InterruptReceiveData), (38 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_InterruptSendData), (40 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptSendData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_IsocReceiveData), (40 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_IsocReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_IsocSendData), (40 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_IsocSendData), (8 bytes).
    Removing usbh_ctlreq.o(.text), (0 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_DevDesc), (8 bytes).
    Removing usbh_ctlreq.o(.text.USBH_GetDescriptor), (58 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_GetDescriptor), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_CfgDesc), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_StringDesc), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_CtlReq), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetAddress), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetCfg), (8 bytes).
    Removing usbh_ctlreq.o(.text.USBH_SetInterface), (28 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetInterface), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetFeature), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_ClrFeature), (8 bytes).
    Removing usbh_ctlreq.o(.text.USBH_GetNextDesc), (12 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_GetNextDesc), (8 bytes).
    Removing usbh_diskio_dma.o(.text), (0 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_initialize), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_status), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_read), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_write), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_ioctl), (8 bytes).
    Removing usbh_conf.o(.text), (0 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspInit), (8 bytes).
    Removing usbh_conf.o(.text.HAL_HCD_MspDeInit), (28 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspDeInit), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_SOF_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_Connect_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_Init), (8 bytes).
    Removing usbh_conf.o(.text.USBH_LL_DeInit), (14 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_DeInit), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_Start), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_Stop), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetSpeed), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_ResetPort), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetLastXferSize), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_OpenPipe), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_ClosePipe), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_SubmitURB), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetURBState), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_DriverVBUS), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_SetToggle), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetToggle), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_Delay), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.USBH_UserProcess), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.OTG_FS_IRQHandler), (8 bytes).
    Removing usbh_msc_bot.o(.text), (0 bytes).
    Removing usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_Reset), (22 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_Reset), (8 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_GetMaxLUN), (8 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Init), (8 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Process), (8 bytes).
    Removing usbh_msc.o(.text), (0 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceInit), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceDeInit), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_ClassRequest), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_Process), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_SOFProcess), (8 bytes).
    Removing usbh_msc.o(.text.USBH_MSC_IsReady), (26 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_IsReady), (8 bytes).
    Removing usbh_msc.o(.text.USBH_MSC_GetMaxLUN), (26 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetMaxLUN), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_UnitIsReady), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetLUNInfo), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_Read), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_RdWrProcess), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_Write), (8 bytes).
    Removing usbh_msc_scsi.o(.text), (0 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_TestUnitReady), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_ReadCapacity), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Inquiry), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_RequestSense), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Write), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Read), (8 bytes).

871 unused section(s) (total 53109 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/malloc/calloc.c         0x00000000   Number         0  calloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    diskio.c                                 0x00000000   Number         0  diskio.o ABSOLUTE
    ff.c                                     0x00000000   Number         0  ff.o ABSOLUTE
    ff_gen_drv.c                             0x00000000   Number         0  ff_gen_drv.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    startup_stm32f401xc.s                    0x00000000   Number         0  startup_stm32f401xc.o ABSOLUTE
    stm32f401_discovery.c                    0x00000000   Number         0  stm32f401_discovery.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dcmi.c                     0x00000000   Number         0  stm32f4xx_hal_dcmi.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_flash.c                    0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    stm32f4xx_hal_flash_ex.c                 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_hcd.c                      0x00000000   Number         0  stm32f4xx_hal_hcd.o ABSOLUTE
    stm32f4xx_hal_i2c.c                      0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_spi.c                      0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_ll_usb.c                       0x00000000   Number         0  stm32f4xx_ll_usb.o ABSOLUTE
    syscall.c                                0x00000000   Number         0  syscall.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    unicode.c                                0x00000000   Number         0  unicode.o ABSOLUTE
    usbh_conf.c                              0x00000000   Number         0  usbh_conf.o ABSOLUTE
    usbh_core.c                              0x00000000   Number         0  usbh_core.o ABSOLUTE
    usbh_ctlreq.c                            0x00000000   Number         0  usbh_ctlreq.o ABSOLUTE
    usbh_diskio_dma.c                        0x00000000   Number         0  usbh_diskio_dma.o ABSOLUTE
    usbh_ioreq.c                             0x00000000   Number         0  usbh_ioreq.o ABSOLUTE
    usbh_msc.c                               0x00000000   Number         0  usbh_msc.o ABSOLUTE
    usbh_msc_bot.c                           0x00000000   Number         0  usbh_msc_bot.o ABSOLUTE
    usbh_msc_scsi.c                          0x00000000   Number         0  usbh_msc_scsi.o ABSOLUTE
    usbh_pipes.c                             0x00000000   Number         0  usbh_pipes.o ABSOLUTE
    RESET                                    0x08000000   Section      404  startup_stm32f401xc.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000194   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000194   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x08000198   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x0800019c   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x0800019c   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x0800019c   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x080001a4   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x080001a4   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001a4   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001a4   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x080001a8   Number         0  startup_stm32f401xc.o(.text)
    .text                                    0x080001a8   Section       36  startup_stm32f401xc.o(.text)
    .text                                    0x080001cc   Section        0  uldiv.o(.text)
    .text                                    0x0800022e   Section        0  memcpya.o(.text)
    .text                                    0x08000252   Section        0  memseta.o(.text)
    .text                                    0x08000276   Section        0  calloc.o(.text)
    .text                                    0x08000292   Section        0  llshl.o(.text)
    .text                                    0x080002b0   Section        0  llushr.o(.text)
    .text                                    0x080002d0   Section       48  init.o(.text)
    [Anonymous Symbol]                       0x08000300   Section        0  stm32f401_discovery.o(.text.BSP_LED_Init)
    [Anonymous Symbol]                       0x0800036c   Section        0  stm32f401_discovery.o(.text.BSP_LED_Off)
    [Anonymous Symbol]                       0x0800038c   Section        0  stm32f401_discovery.o(.text.BSP_LED_On)
    [Anonymous Symbol]                       0x080003ac   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x080003b0   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x080003b4   Section        0  ff_gen_drv.o(.text.FATFS_LinkDriver)
    [Anonymous Symbol]                       0x08000400   Section        0  ff_gen_drv.o(.text.FATFS_UnLinkDriver)
    [Anonymous Symbol]                       0x08000430   Section        0  stm32f4xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x08000458   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x080005f8   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08000604   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08000610   Section        0  usbh_conf.o(.text.HAL_HCD_Connect_Callback)
    [Anonymous Symbol]                       0x08000618   Section        0  usbh_conf.o(.text.HAL_HCD_Disconnect_Callback)
    [Anonymous Symbol]                       0x08000620   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame)
    [Anonymous Symbol]                       0x08000628   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed)
    [Anonymous Symbol]                       0x08000630   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState)
    [Anonymous Symbol]                       0x08000640   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount)
    [Anonymous Symbol]                       0x0800064c   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt)
    [Anonymous Symbol]                       0x08000674   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init)
    [Anonymous Symbol]                       0x0800071c   Section        0  usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback)
    [Anonymous Symbol]                       0x08000720   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest)
    [Anonymous Symbol]                       0x080007c4   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler)
    [Anonymous Symbol]                       0x08001114   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init)
    [Anonymous Symbol]                       0x080011a0   Section        0  usbh_conf.o(.text.HAL_HCD_MspInit)
    [Anonymous Symbol]                       0x08001274   Section        0  usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback)
    [Anonymous Symbol]                       0x0800127c   Section        0  usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback)
    [Anonymous Symbol]                       0x08001284   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort)
    [Anonymous Symbol]                       0x0800128c   Section        0  usbh_conf.o(.text.HAL_HCD_SOF_Callback)
    [Anonymous Symbol]                       0x08001294   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start)
    [Anonymous Symbol]                       0x080012c4   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop)
    [Anonymous Symbol]                       0x080012ec   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x08001308   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x08001340   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08001388   Section        0  stm32f4xx_hal.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x0800138c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x080013b0   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x08001408   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x08001428   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x0800158c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x080015fc   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x080019a8   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x080019d4   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x080019d8   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x080019dc   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x080019e0   Section        0  stm32f4xx_it.o(.text.OTG_FS_IRQHandler)
    [Anonymous Symbol]                       0x080019ec   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x080019f0   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080019f4   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x080019f8   Section        0  system_stm32f4xx.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08001a50   Section        0  usbh_pipes.o(.text.USBH_AllocPipe)
    [Anonymous Symbol]                       0x08001b24   Section        0  usbh_ioreq.o(.text.USBH_BulkReceiveData)
    [Anonymous Symbol]                       0x08001b4c   Section        0  usbh_ioreq.o(.text.USBH_BulkSendData)
    [Anonymous Symbol]                       0x08001b80   Section        0  usbh_pipes.o(.text.USBH_ClosePipe)
    [Anonymous Symbol]                       0x08001b8c   Section        0  usbh_ctlreq.o(.text.USBH_ClrFeature)
    [Anonymous Symbol]                       0x08001ba8   Section        0  usbh_ioreq.o(.text.USBH_CtlReceiveData)
    [Anonymous Symbol]                       0x08001bd0   Section        0  usbh_ctlreq.o(.text.USBH_CtlReq)
    [Anonymous Symbol]                       0x08001db4   Section        0  usbh_ioreq.o(.text.USBH_CtlSendData)
    [Anonymous Symbol]                       0x08001de8   Section        0  usbh_ioreq.o(.text.USBH_CtlSendSetup)
    [Anonymous Symbol]                       0x08001e0c   Section        0  usbh_conf.o(.text.USBH_Delay)
    [Anonymous Symbol]                       0x08001e10   Section        0  usbh_core.o(.text.USBH_FindInterface)
    [Anonymous Symbol]                       0x08001f0c   Section        0  usbh_pipes.o(.text.USBH_FreePipe)
    [Anonymous Symbol]                       0x08001f24   Section        0  usbh_ctlreq.o(.text.USBH_Get_CfgDesc)
    [Anonymous Symbol]                       0x080021b4   Section        0  usbh_ctlreq.o(.text.USBH_Get_DevDesc)
    [Anonymous Symbol]                       0x08002270   Section        0  usbh_ctlreq.o(.text.USBH_Get_StringDesc)
    [Anonymous Symbol]                       0x0800232c   Section        0  usbh_core.o(.text.USBH_Init)
    [Anonymous Symbol]                       0x080023a4   Section        0  usbh_conf.o(.text.USBH_LL_ClosePipe)
    [Anonymous Symbol]                       0x080023b4   Section        0  usbh_core.o(.text.USBH_LL_Connect)
    [Anonymous Symbol]                       0x080023cc   Section        0  usbh_core.o(.text.USBH_LL_Disconnect)
    [Anonymous Symbol]                       0x080023fc   Section        0  usbh_conf.o(.text.USBH_LL_DriverVBUS)
    [Anonymous Symbol]                       0x0800241c   Section        0  usbh_conf.o(.text.USBH_LL_GetLastXferSize)
    [Anonymous Symbol]                       0x08002424   Section        0  usbh_conf.o(.text.USBH_LL_GetSpeed)
    [Anonymous Symbol]                       0x08002438   Section        0  usbh_conf.o(.text.USBH_LL_GetToggle)
    [Anonymous Symbol]                       0x08002458   Section        0  usbh_conf.o(.text.USBH_LL_GetURBState)
    [Anonymous Symbol]                       0x08002460   Section        0  usbh_core.o(.text.USBH_LL_IncTimer)
    [Anonymous Symbol]                       0x08002480   Section        0  usbh_conf.o(.text.USBH_LL_Init)
    [Anonymous Symbol]                       0x080024c8   Section        0  usbh_conf.o(.text.USBH_LL_OpenPipe)
    [Anonymous Symbol]                       0x080024e8   Section        0  usbh_core.o(.text.USBH_LL_PortDisabled)
    [Anonymous Symbol]                       0x080024f8   Section        0  usbh_core.o(.text.USBH_LL_PortEnabled)
    [Anonymous Symbol]                       0x08002500   Section        0  usbh_conf.o(.text.USBH_LL_ResetPort)
    [Anonymous Symbol]                       0x08002510   Section        0  usbh_core.o(.text.USBH_LL_SetTimer)
    [Anonymous Symbol]                       0x08002518   Section        0  usbh_conf.o(.text.USBH_LL_SetToggle)
    [Anonymous Symbol]                       0x0800253c   Section        0  usbh_conf.o(.text.USBH_LL_Start)
    [Anonymous Symbol]                       0x0800254c   Section        0  usbh_conf.o(.text.USBH_LL_Stop)
    [Anonymous Symbol]                       0x0800255c   Section        0  usbh_conf.o(.text.USBH_LL_SubmitURB)
    [Anonymous Symbol]                       0x08002580   Section        0  usbh_msc_bot.o(.text.USBH_MSC_BOT_Init)
    [Anonymous Symbol]                       0x080025a8   Section        0  usbh_msc_bot.o(.text.USBH_MSC_BOT_Process)
    [Anonymous Symbol]                       0x080027e0   Section        0  usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN)
    USBH_MSC_ClassRequest                    0x080027f9   Thumb Code   120  usbh_msc.o(.text.USBH_MSC_ClassRequest)
    [Anonymous Symbol]                       0x080027f8   Section        0  usbh_msc.o(.text.USBH_MSC_ClassRequest)
    [Anonymous Symbol]                       0x08002870   Section        0  usbh_msc.o(.text.USBH_MSC_GetLUNInfo)
    USBH_MSC_InterfaceDeInit                 0x080028c9   Thumb Code    78  usbh_msc.o(.text.USBH_MSC_InterfaceDeInit)
    [Anonymous Symbol]                       0x080028c8   Section        0  usbh_msc.o(.text.USBH_MSC_InterfaceDeInit)
    USBH_MSC_InterfaceInit                   0x08002919   Thumb Code   270  usbh_msc.o(.text.USBH_MSC_InterfaceInit)
    [Anonymous Symbol]                       0x08002918   Section        0  usbh_msc.o(.text.USBH_MSC_InterfaceInit)
    USBH_MSC_Process                         0x08002a29   Thumb Code   514  usbh_msc.o(.text.USBH_MSC_Process)
    [Anonymous Symbol]                       0x08002a28   Section        0  usbh_msc.o(.text.USBH_MSC_Process)
    USBH_MSC_RdWrProcess                     0x08002c2d   Thumb Code   142  usbh_msc.o(.text.USBH_MSC_RdWrProcess)
    [Anonymous Symbol]                       0x08002c2c   Section        0  usbh_msc.o(.text.USBH_MSC_RdWrProcess)
    [Anonymous Symbol]                       0x08002cbc   Section        0  usbh_msc.o(.text.USBH_MSC_Read)
    [Anonymous Symbol]                       0x08002d30   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry)
    [Anonymous Symbol]                       0x08002de0   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read)
    [Anonymous Symbol]                       0x08002e6c   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity)
    [Anonymous Symbol]                       0x08002edc   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense)
    [Anonymous Symbol]                       0x08002f68   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady)
    [Anonymous Symbol]                       0x08002fb0   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write)
    USBH_MSC_SOFProcess                      0x0800303d   Thumb Code     4  usbh_msc.o(.text.USBH_MSC_SOFProcess)
    [Anonymous Symbol]                       0x0800303c   Section        0  usbh_msc.o(.text.USBH_MSC_SOFProcess)
    [Anonymous Symbol]                       0x08003040   Section        0  usbh_msc.o(.text.USBH_MSC_UnitIsReady)
    [Anonymous Symbol]                       0x08003068   Section        0  usbh_msc.o(.text.USBH_MSC_Write)
    [Anonymous Symbol]                       0x080030dc   Section        0  usbh_pipes.o(.text.USBH_OpenPipe)
    [Anonymous Symbol]                       0x080030f8   Section        0  usbh_core.o(.text.USBH_Process)
    [Anonymous Symbol]                       0x080035a4   Section        0  usbh_core.o(.text.USBH_RegisterClass)
    [Anonymous Symbol]                       0x080035c0   Section        0  usbh_core.o(.text.USBH_SelectInterface)
    [Anonymous Symbol]                       0x080035d4   Section        0  usbh_ctlreq.o(.text.USBH_SetAddress)
    [Anonymous Symbol]                       0x080035f0   Section        0  usbh_ctlreq.o(.text.USBH_SetCfg)
    [Anonymous Symbol]                       0x0800360c   Section        0  usbh_ctlreq.o(.text.USBH_SetFeature)
    [Anonymous Symbol]                       0x08003628   Section        0  usbh_core.o(.text.USBH_Start)
    USBH_UserProcess                         0x0800363d   Thumb Code    70  main.o(.text.USBH_UserProcess)
    [Anonymous Symbol]                       0x0800363c   Section        0  main.o(.text.USBH_UserProcess)
    [Anonymous Symbol]                       0x08003684   Section        0  usbh_diskio_dma.o(.text.USBH_initialize)
    [Anonymous Symbol]                       0x08003688   Section        0  usbh_diskio_dma.o(.text.USBH_ioctl)
    [Anonymous Symbol]                       0x0800370c   Section        0  usbh_diskio_dma.o(.text.USBH_read)
    [Anonymous Symbol]                       0x080037c8   Section        0  usbh_diskio_dma.o(.text.USBH_status)
    [Anonymous Symbol]                       0x080037e0   Section        0  usbh_diskio_dma.o(.text.USBH_write)
    [Anonymous Symbol]                       0x080038ac   Section        0  stm32f4xx_ll_usb.o(.text.USB_CoreInit)
    [Anonymous Symbol]                       0x08003b24   Section        0  stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt)
    [Anonymous Symbol]                       0x08003b34   Section        0  stm32f4xx_ll_usb.o(.text.USB_DriveVbus)
    [Anonymous Symbol]                       0x08003b80   Section        0  stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt)
    [Anonymous Symbol]                       0x08003b90   Section        0  stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo)
    [Anonymous Symbol]                       0x08003c50   Section        0  stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo)
    [Anonymous Symbol]                       0x08003d10   Section        0  stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame)
    [Anonymous Symbol]                       0x08003d18   Section        0  stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed)
    [Anonymous Symbol]                       0x08003d30   Section        0  stm32f4xx_ll_usb.o(.text.USB_GetMode)
    [Anonymous Symbol]                       0x08003d38   Section        0  stm32f4xx_ll_usb.o(.text.USB_HC_Halt)
    [Anonymous Symbol]                       0x08003ebc   Section        0  stm32f4xx_ll_usb.o(.text.USB_HC_Init)
    [Anonymous Symbol]                       0x08003fc8   Section        0  stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt)
    [Anonymous Symbol]                       0x08003fd0   Section        0  stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer)
    [Anonymous Symbol]                       0x08004264   Section        0  stm32f4xx_ll_usb.o(.text.USB_HostInit)
    [Anonymous Symbol]                       0x08004518   Section        0  stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel)
    [Anonymous Symbol]                       0x08004558   Section        0  stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts)
    [Anonymous Symbol]                       0x08004568   Section        0  stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts)
    [Anonymous Symbol]                       0x08004570   Section        0  stm32f4xx_ll_usb.o(.text.USB_ReadPacket)
    [Anonymous Symbol]                       0x0800461c   Section        0  stm32f4xx_ll_usb.o(.text.USB_ResetPort)
    [Anonymous Symbol]                       0x0800465c   Section        0  stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode)
    [Anonymous Symbol]                       0x080049c4   Section        0  stm32f4xx_ll_usb.o(.text.USB_StopHost)
    [Anonymous Symbol]                       0x080052fc   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    create_chain                             0x08005301   Thumb Code   286  ff.o(.text.create_chain)
    [Anonymous Symbol]                       0x08005300   Section        0  ff.o(.text.create_chain)
    dir_find                                 0x08005421   Thumb Code  1628  ff.o(.text.dir_find)
    [Anonymous Symbol]                       0x08005420   Section        0  ff.o(.text.dir_find)
    dir_next                                 0x08005a7d   Thumb Code   350  ff.o(.text.dir_next)
    [Anonymous Symbol]                       0x08005a7c   Section        0  ff.o(.text.dir_next)
    dir_register                             0x08005bdd   Thumb Code  2152  ff.o(.text.dir_register)
    [Anonymous Symbol]                       0x08005bdc   Section        0  ff.o(.text.dir_register)
    dir_sdi                                  0x08006445   Thumb Code   166  ff.o(.text.dir_sdi)
    [Anonymous Symbol]                       0x08006444   Section        0  ff.o(.text.dir_sdi)
    [Anonymous Symbol]                       0x080064ec   Section        0  diskio.o(.text.disk_initialize)
    [Anonymous Symbol]                       0x08006520   Section        0  diskio.o(.text.disk_ioctl)
    [Anonymous Symbol]                       0x08006538   Section        0  diskio.o(.text.disk_read)
    [Anonymous Symbol]                       0x08006558   Section        0  diskio.o(.text.disk_status)
    [Anonymous Symbol]                       0x08006570   Section        0  diskio.o(.text.disk_write)
    [Anonymous Symbol]                       0x08006590   Section        0  ff.o(.text.f_close)
    [Anonymous Symbol]                       0x080065fc   Section        0  ff.o(.text.f_mount)
    [Anonymous Symbol]                       0x08006714   Section        0  ff.o(.text.f_open)
    [Anonymous Symbol]                       0x08006aec   Section        0  ff.o(.text.f_read)
    [Anonymous Symbol]                       0x08006df4   Section        0  ff.o(.text.f_sync)
    [Anonymous Symbol]                       0x08006eb4   Section        0  ff.o(.text.f_write)
    [Anonymous Symbol]                       0x080071fc   Section        0  unicode.o(.text.ff_convert)
    [Anonymous Symbol]                       0x080073a8   Section        0  syscall.o(.text.ff_memalloc)
    [Anonymous Symbol]                       0x080073ac   Section        0  syscall.o(.text.ff_memfree)
    [Anonymous Symbol]                       0x080073b0   Section        0  unicode.o(.text.ff_wtoupper)
    find_volume                              0x08007481   Thumb Code  1342  ff.o(.text.find_volume)
    [Anonymous Symbol]                       0x08007480   Section        0  ff.o(.text.find_volume)
    follow_path                              0x080079c1   Thumb Code  1872  ff.o(.text.follow_path)
    [Anonymous Symbol]                       0x080079c0   Section        0  ff.o(.text.follow_path)
    get_fat                                  0x08008111   Thumb Code   200  ff.o(.text.get_fat)
    [Anonymous Symbol]                       0x08008110   Section        0  ff.o(.text.get_fat)
    [Anonymous Symbol]                       0x080081d8   Section        0  diskio.o(.text.get_fattime)
    [Anonymous Symbol]                       0x080081dc   Section        0  main.o(.text.main)
    move_window                              0x080083f5   Thumb Code   124  ff.o(.text.move_window)
    [Anonymous Symbol]                       0x080083f4   Section        0  ff.o(.text.move_window)
    put_fat                                  0x08008471   Thumb Code   280  ff.o(.text.put_fat)
    [Anonymous Symbol]                       0x08008470   Section        0  ff.o(.text.put_fat)
    sync_fs                                  0x08008589   Thumb Code   204  ff.o(.text.sync_fs)
    [Anonymous Symbol]                       0x08008588   Section        0  ff.o(.text.sync_fs)
    sync_window                              0x08008655   Thumb Code    90  ff.o(.text.sync_window)
    [Anonymous Symbol]                       0x08008654   Section        0  ff.o(.text.sync_window)
    i.__scatterload_copy                     0x080086ae   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x080086bc   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080086be   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.free                                   0x080086cc   Section        0  malloc.o(i.free)
    i.malloc                                 0x0800871c   Section        0  malloc.o(i.malloc)
    ExCvt                                    0x08008798   Data         128  ff.o(.rodata.ExCvt)
    [Anonymous Symbol]                       0x08008798   Section        0  ff.o(.rodata.ExCvt)
    Tbl                                      0x08008820   Data         256  unicode.o(.rodata.Tbl)
    [Anonymous Symbol]                       0x08008820   Section        0  unicode.o(.rodata.Tbl)
    .L.str                                   0x08008934   Data          10  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08008934   Section        0  main.o(.rodata.str1.1)
    .L.str.1                                 0x0800893d   Data           1  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800893e   Section        0  usbh_msc.o(.rodata.str1.1)
    ff_wtoupper.cvt2                         0x08008942   Data         188  unicode.o(.rodata.str2.2)
    [Anonymous Symbol]                       0x08008942   Section        0  unicode.o(.rodata.str2.2)
    ff_wtoupper.cvt1                         0x080089fe   Data         498  unicode.o(.rodata.str2.2)
    .data                                    0x20000000   Section        4  mvars.o(.data)
    .data                                    0x20000004   Section        4  mvars.o(.data)
    .L_MergedGlobals                         0x20000008   Data           8  stm32f4xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000008   Section        0  stm32f4xx_hal.o(.data..L_MergedGlobals)
    .L_MergedGlobals                         0x20000048   Data          44  ff.o(.bss..L_MergedGlobals)
    Fsid                                     0x20000048   Data           2  ff.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000048   Section        0  ff.o(.bss..L_MergedGlobals)
    FatFs                                    0x2000004c   Data           8  ff.o(.bss..L_MergedGlobals)
    Files                                    0x20000054   Data          32  ff.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000074   Data           5  main.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000074   Section        0  main.o(.bss..L_MergedGlobals)
    scratch                                  0x20000da8   Data         512  usbh_diskio_dma.o(.bss.scratch)
    [Anonymous Symbol]                       0x20000da8   Section        0  usbh_diskio_dma.o(.bss.scratch)
    HEAP                                     0x20000fb0   Section     1024  startup_stm32f401xc.o(HEAP)
    STACK                                    0x200013b0   Section     2048  startup_stm32f401xc.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000194   Number         0  startup_stm32f401xc.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f401xc.o(RESET)
    __Vectors_End                            0x08000194   Data           0  startup_stm32f401xc.o(RESET)
    __main                                   0x08000195   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000195   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x08000199   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x0800019d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x0800019d   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x0800019d   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x0800019d   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001a5   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001a5   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001a9   Thumb Code     8  startup_stm32f401xc.o(.text)
    ADC_IRQHandler                           0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    EXTI0_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    EXTI15_10_IRQHandler                     0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    EXTI1_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    EXTI2_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    EXTI3_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    EXTI4_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    EXTI9_5_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    FLASH_IRQHandler                         0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    FPU_IRQHandler                           0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    I2C1_ER_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    I2C1_EV_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    I2C2_ER_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    I2C2_EV_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    I2C3_ER_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    I2C3_EV_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    PVD_IRQHandler                           0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    RCC_IRQHandler                           0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    RTC_Alarm_IRQHandler                     0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    RTC_WKUP_IRQHandler                      0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    SDIO_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    SPI1_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    SPI2_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    SPI3_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    SPI4_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    TIM1_CC_IRQHandler                       0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    TIM2_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    TIM3_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    TIM4_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    TIM5_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    USART1_IRQHandler                        0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    USART2_IRQHandler                        0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    USART6_IRQHandler                        0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    WWDG_IRQHandler                          0x080001c3   Thumb Code     0  startup_stm32f401xc.o(.text)
    __aeabi_uldivmod                         0x080001cd   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x0800022f   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0800022f   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0800022f   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x08000253   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000253   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000253   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000261   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000261   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000261   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000265   Thumb Code    18  memseta.o(.text)
    calloc                                   0x08000277   Thumb Code    28  calloc.o(.text)
    __aeabi_llsl                             0x08000293   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000293   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x080002b1   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080002b1   Thumb Code     0  llushr.o(.text)
    __scatterload                            0x080002d1   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x080002d1   Thumb Code     0  init.o(.text)
    BSP_LED_Init                             0x08000301   Thumb Code   108  stm32f401_discovery.o(.text.BSP_LED_Init)
    BSP_LED_Off                              0x0800036d   Thumb Code    32  stm32f401_discovery.o(.text.BSP_LED_Off)
    BSP_LED_On                               0x0800038d   Thumb Code    32  stm32f401_discovery.o(.text.BSP_LED_On)
    BusFault_Handler                         0x080003ad   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    DebugMon_Handler                         0x080003b1   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    FATFS_LinkDriver                         0x080003b5   Thumb Code    76  ff_gen_drv.o(.text.FATFS_LinkDriver)
    FATFS_UnLinkDriver                       0x08000401   Thumb Code    48  ff_gen_drv.o(.text.FATFS_UnLinkDriver)
    HAL_Delay                                0x08000431   Thumb Code    40  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x08000459   Thumb Code   414  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x080005f9   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000605   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_HCD_Connect_Callback                 0x08000611   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_Connect_Callback)
    HAL_HCD_Disconnect_Callback              0x08000619   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_Disconnect_Callback)
    HAL_HCD_GetCurrentFrame                  0x08000621   Thumb Code     6  stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame)
    HAL_HCD_GetCurrentSpeed                  0x08000629   Thumb Code     6  stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed)
    HAL_HCD_HC_GetURBState                   0x08000631   Thumb Code    14  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState)
    HAL_HCD_HC_GetXferCount                  0x08000641   Thumb Code    12  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount)
    HAL_HCD_HC_Halt                          0x0800064d   Thumb Code    38  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt)
    HAL_HCD_HC_Init                          0x08000675   Thumb Code   166  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init)
    HAL_HCD_HC_NotifyURBChange_Callback      0x0800071d   Thumb Code     2  usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback)
    HAL_HCD_HC_SubmitRequest                 0x08000721   Thumb Code   162  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest)
    HAL_HCD_IRQHandler                       0x080007c5   Thumb Code  2384  stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler)
    HAL_HCD_Init                             0x08001115   Thumb Code   138  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init)
    HAL_HCD_MspInit                          0x080011a1   Thumb Code   210  usbh_conf.o(.text.HAL_HCD_MspInit)
    HAL_HCD_PortDisabled_Callback            0x08001275   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback)
    HAL_HCD_PortEnabled_Callback             0x0800127d   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback)
    HAL_HCD_ResetPort                        0x08001285   Thumb Code     6  stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort)
    HAL_HCD_SOF_Callback                     0x0800128d   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_SOF_Callback)
    HAL_HCD_Start                            0x08001295   Thumb Code    46  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start)
    HAL_HCD_Stop                             0x080012c5   Thumb Code    38  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop)
    HAL_IncTick                              0x080012ed   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x08001309   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x08001341   Thumb Code    72  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08001389   Thumb Code     2  stm32f4xx_hal.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x0800138d   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x080013b1   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08001409   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08001429   Thumb Code   356  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetSysClockFreq                  0x0800158d   Thumb Code   112  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080015fd   Thumb Code   940  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080019a9   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HardFault_Handler                        0x080019d5   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    MemManage_Handler                        0x080019d9   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x080019dd   Thumb Code     2  stm32f4xx_it.o(.text.NMI_Handler)
    OTG_FS_IRQHandler                        0x080019e1   Thumb Code    12  stm32f4xx_it.o(.text.OTG_FS_IRQHandler)
    PendSV_Handler                           0x080019ed   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x080019f1   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x080019f5   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemInit                               0x080019f9   Thumb Code    86  system_stm32f4xx.o(.text.SystemInit)
    USBH_AllocPipe                           0x08001a51   Thumb Code   210  usbh_pipes.o(.text.USBH_AllocPipe)
    USBH_BulkReceiveData                     0x08001b25   Thumb Code    38  usbh_ioreq.o(.text.USBH_BulkReceiveData)
    USBH_BulkSendData                        0x08001b4d   Thumb Code    50  usbh_ioreq.o(.text.USBH_BulkSendData)
    USBH_ClosePipe                           0x08001b81   Thumb Code    10  usbh_pipes.o(.text.USBH_ClosePipe)
    USBH_ClrFeature                          0x08001b8d   Thumb Code    26  usbh_ctlreq.o(.text.USBH_ClrFeature)
    USBH_CtlReceiveData                      0x08001ba9   Thumb Code    38  usbh_ioreq.o(.text.USBH_CtlReceiveData)
    USBH_CtlReq                              0x08001bd1   Thumb Code   482  usbh_ctlreq.o(.text.USBH_CtlReq)
    USBH_CtlSendData                         0x08001db5   Thumb Code    50  usbh_ioreq.o(.text.USBH_CtlSendData)
    USBH_CtlSendSetup                        0x08001de9   Thumb Code    36  usbh_ioreq.o(.text.USBH_CtlSendSetup)
    USBH_Delay                               0x08001e0d   Thumb Code     4  usbh_conf.o(.text.USBH_Delay)
    USBH_FindInterface                       0x08001e11   Thumb Code   250  usbh_core.o(.text.USBH_FindInterface)
    USBH_FreePipe                            0x08001f0d   Thumb Code    24  usbh_pipes.o(.text.USBH_FreePipe)
    USBH_Get_CfgDesc                         0x08001f25   Thumb Code   656  usbh_ctlreq.o(.text.USBH_Get_CfgDesc)
    USBH_Get_DevDesc                         0x080021b5   Thumb Code   186  usbh_ctlreq.o(.text.USBH_Get_DevDesc)
    USBH_Get_StringDesc                      0x08002271   Thumb Code   188  usbh_ctlreq.o(.text.USBH_Get_StringDesc)
    USBH_Init                                0x0800232d   Thumb Code   118  usbh_core.o(.text.USBH_Init)
    USBH_LL_ClosePipe                        0x080023a5   Thumb Code    14  usbh_conf.o(.text.USBH_LL_ClosePipe)
    USBH_LL_Connect                          0x080023b5   Thumb Code    22  usbh_core.o(.text.USBH_LL_Connect)
    USBH_LL_Disconnect                       0x080023cd   Thumb Code    46  usbh_core.o(.text.USBH_LL_Disconnect)
    USBH_LL_DriverVBUS                       0x080023fd   Thumb Code    32  usbh_conf.o(.text.USBH_LL_DriverVBUS)
    USBH_LL_GetLastXferSize                  0x0800241d   Thumb Code     8  usbh_conf.o(.text.USBH_LL_GetLastXferSize)
    USBH_LL_GetSpeed                         0x08002425   Thumb Code    20  usbh_conf.o(.text.USBH_LL_GetSpeed)
    USBH_LL_GetToggle                        0x08002439   Thumb Code    32  usbh_conf.o(.text.USBH_LL_GetToggle)
    USBH_LL_GetURBState                      0x08002459   Thumb Code     8  usbh_conf.o(.text.USBH_LL_GetURBState)
    USBH_LL_IncTimer                         0x08002461   Thumb Code    30  usbh_core.o(.text.USBH_LL_IncTimer)
    USBH_LL_Init                             0x08002481   Thumb Code    70  usbh_conf.o(.text.USBH_LL_Init)
    USBH_LL_OpenPipe                         0x080024c9   Thumb Code    30  usbh_conf.o(.text.USBH_LL_OpenPipe)
    USBH_LL_PortDisabled                     0x080024e9   Thumb Code    14  usbh_core.o(.text.USBH_LL_PortDisabled)
    USBH_LL_PortEnabled                      0x080024f9   Thumb Code     8  usbh_core.o(.text.USBH_LL_PortEnabled)
    USBH_LL_ResetPort                        0x08002501   Thumb Code    14  usbh_conf.o(.text.USBH_LL_ResetPort)
    USBH_LL_SetTimer                         0x08002511   Thumb Code     6  usbh_core.o(.text.USBH_LL_SetTimer)
    USBH_LL_SetToggle                        0x08002519   Thumb Code    36  usbh_conf.o(.text.USBH_LL_SetToggle)
    USBH_LL_Start                            0x0800253d   Thumb Code    14  usbh_conf.o(.text.USBH_LL_Start)
    USBH_LL_Stop                             0x0800254d   Thumb Code    14  usbh_conf.o(.text.USBH_LL_Stop)
    USBH_LL_SubmitURB                        0x0800255d   Thumb Code    34  usbh_conf.o(.text.USBH_LL_SubmitURB)
    USBH_MSC_BOT_Init                        0x08002581   Thumb Code    40  usbh_msc_bot.o(.text.USBH_MSC_BOT_Init)
    USBH_MSC_BOT_Process                     0x080025a9   Thumb Code   566  usbh_msc_bot.o(.text.USBH_MSC_BOT_Process)
    USBH_MSC_BOT_REQ_GetMaxLUN               0x080027e1   Thumb Code    22  usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN)
    USBH_MSC_GetLUNInfo                      0x08002871   Thumb Code    88  usbh_msc.o(.text.USBH_MSC_GetLUNInfo)
    USBH_MSC_Read                            0x08002cbd   Thumb Code   114  usbh_msc.o(.text.USBH_MSC_Read)
    USBH_MSC_SCSI_Inquiry                    0x08002d31   Thumb Code   176  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry)
    USBH_MSC_SCSI_Read                       0x08002de1   Thumb Code   140  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read)
    USBH_MSC_SCSI_ReadCapacity               0x08002e6d   Thumb Code   112  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity)
    USBH_MSC_SCSI_RequestSense               0x08002edd   Thumb Code   140  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense)
    USBH_MSC_SCSI_TestUnitReady              0x08002f69   Thumb Code    72  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady)
    USBH_MSC_SCSI_Write                      0x08002fb1   Thumb Code   140  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write)
    USBH_MSC_UnitIsReady                     0x08003041   Thumb Code    38  usbh_msc.o(.text.USBH_MSC_UnitIsReady)
    USBH_MSC_Write                           0x08003069   Thumb Code   114  usbh_msc.o(.text.USBH_MSC_Write)
    USBH_OpenPipe                            0x080030dd   Thumb Code    26  usbh_pipes.o(.text.USBH_OpenPipe)
    USBH_Process                             0x080030f9   Thumb Code  1196  usbh_core.o(.text.USBH_Process)
    USBH_RegisterClass                       0x080035a5   Thumb Code    26  usbh_core.o(.text.USBH_RegisterClass)
    USBH_SelectInterface                     0x080035c1   Thumb Code    18  usbh_core.o(.text.USBH_SelectInterface)
    USBH_SetAddress                          0x080035d5   Thumb Code    26  usbh_ctlreq.o(.text.USBH_SetAddress)
    USBH_SetCfg                              0x080035f1   Thumb Code    26  usbh_ctlreq.o(.text.USBH_SetCfg)
    USBH_SetFeature                          0x0800360d   Thumb Code    26  usbh_ctlreq.o(.text.USBH_SetFeature)
    USBH_Start                               0x08003629   Thumb Code    20  usbh_core.o(.text.USBH_Start)
    USBH_initialize                          0x08003685   Thumb Code     4  usbh_diskio_dma.o(.text.USBH_initialize)
    USBH_ioctl                               0x08003689   Thumb Code   130  usbh_diskio_dma.o(.text.USBH_ioctl)
    USBH_read                                0x0800370d   Thumb Code   188  usbh_diskio_dma.o(.text.USBH_read)
    USBH_status                              0x080037c9   Thumb Code    24  usbh_diskio_dma.o(.text.USBH_status)
    USBH_write                               0x080037e1   Thumb Code   204  usbh_diskio_dma.o(.text.USBH_write)
    USB_CoreInit                             0x080038ad   Thumb Code   630  stm32f4xx_ll_usb.o(.text.USB_CoreInit)
    USB_DisableGlobalInt                     0x08003b25   Thumb Code    14  stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt)
    USB_DriveVbus                            0x08003b35   Thumb Code    76  stm32f4xx_ll_usb.o(.text.USB_DriveVbus)
    USB_EnableGlobalInt                      0x08003b81   Thumb Code    14  stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt)
    USB_FlushRxFifo                          0x08003b91   Thumb Code   190  stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo)
    USB_FlushTxFifo                          0x08003c51   Thumb Code   190  stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo)
    USB_GetCurrentFrame                      0x08003d11   Thumb Code     8  stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame)
    USB_GetHostSpeed                         0x08003d19   Thumb Code    22  stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed)
    USB_GetMode                              0x08003d31   Thumb Code     8  stm32f4xx_ll_usb.o(.text.USB_GetMode)
    USB_HC_Halt                              0x08003d39   Thumb Code   386  stm32f4xx_ll_usb.o(.text.USB_HC_Halt)
    USB_HC_Init                              0x08003ebd   Thumb Code   266  stm32f4xx_ll_usb.o(.text.USB_HC_Init)
    USB_HC_ReadInterrupt                     0x08003fc9   Thumb Code     8  stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt)
    USB_HC_StartXfer                         0x08003fd1   Thumb Code   644  stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer)
    USB_HostInit                             0x08004265   Thumb Code   690  stm32f4xx_ll_usb.o(.text.USB_HostInit)
    USB_InitFSLSPClkSel                      0x08004519   Thumb Code    64  stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel)
    USB_ReadChInterrupts                     0x08004559   Thumb Code    16  stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts)
    USB_ReadInterrupts                       0x08004569   Thumb Code     8  stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts)
    USB_ReadPacket                           0x08004571   Thumb Code   170  stm32f4xx_ll_usb.o(.text.USB_ReadPacket)
    USB_ResetPort                            0x0800461d   Thumb Code    62  stm32f4xx_ll_usb.o(.text.USB_ResetPort)
    USB_SetCurrentMode                       0x0800465d   Thumb Code   872  stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode)
    USB_StopHost                             0x080049c5   Thumb Code  2360  stm32f4xx_ll_usb.o(.text.USB_StopHost)
    UsageFault_Handler                       0x080052fd   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    disk_initialize                          0x080064ed   Thumb Code    50  diskio.o(.text.disk_initialize)
    disk_ioctl                               0x08006521   Thumb Code    22  diskio.o(.text.disk_ioctl)
    disk_read                                0x08006539   Thumb Code    32  diskio.o(.text.disk_read)
    disk_status                              0x08006559   Thumb Code    22  diskio.o(.text.disk_status)
    disk_write                               0x08006571   Thumb Code    32  diskio.o(.text.disk_write)
    f_close                                  0x08006591   Thumb Code   108  ff.o(.text.f_close)
    f_mount                                  0x080065fd   Thumb Code   280  ff.o(.text.f_mount)
    f_open                                   0x08006715   Thumb Code   984  ff.o(.text.f_open)
    f_read                                   0x08006aed   Thumb Code   776  ff.o(.text.f_read)
    f_sync                                   0x08006df5   Thumb Code   192  ff.o(.text.f_sync)
    f_write                                  0x08006eb5   Thumb Code   838  ff.o(.text.f_write)
    ff_convert                               0x080071fd   Thumb Code   426  unicode.o(.text.ff_convert)
    ff_memalloc                              0x080073a9   Thumb Code     4  syscall.o(.text.ff_memalloc)
    ff_memfree                               0x080073ad   Thumb Code     4  syscall.o(.text.ff_memfree)
    ff_wtoupper                              0x080073b1   Thumb Code   208  unicode.o(.text.ff_wtoupper)
    get_fattime                              0x080081d9   Thumb Code     4  diskio.o(.text.get_fattime)
    main                                     0x080081dd   Thumb Code   500  main.o(.text.main)
    __scatterload_copy                       0x080086af   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x080086bd   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080086bf   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    free                                     0x080086cd   Thumb Code    76  malloc.o(i.free)
    malloc                                   0x0800871d   Thumb Code    92  malloc.o(i.malloc)
    AHBPrescTable                            0x08008788   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    GPIO_PIN                                 0x08008818   Data           8  stm32f401_discovery.o(.rodata.GPIO_PIN)
    USBH_Driver                              0x08008920   Data          20  usbh_diskio_dma.o(.rodata.USBH_Driver)
    Region$$Table$$Base                      0x08008bf0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08008c10   Number         0  anon$$obj.o(Region$$Table)
    __microlib_freelist                      0x20000000   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x20000004   Data           4  mvars.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f4xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x2000000c   Data           4  stm32f4xx_hal.o(.data..L_MergedGlobals)
    GPIO_PORT                                0x20000010   Data          16  stm32f401_discovery.o(.data.GPIO_PORT)
    SystemCoreClock                          0x20000020   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    USBH_msc                                 0x20000024   Data          32  usbh_msc.o(.data.USBH_msc)
    Appli_state                              0x20000074   Data           1  main.o(.bss..L_MergedGlobals)
    USBDISKPath                              0x20000075   Data           4  main.o(.bss..L_MergedGlobals)
    MyFile                                   0x2000007c   Data         560  main.o(.bss.MyFile)
    USBDISKFatFs                             0x200002ac   Data         564  main.o(.bss.USBDISKFatFs)
    disk                                     0x200004e0   Data          16  ff_gen_drv.o(.bss.disk)
    hUSB_Host                                0x200004f0   Data        1240  main.o(.bss.hUSB_Host)
    hhcd                                     0x200009c8   Data         992  usbh_conf.o(.bss.hhcd)
    uwTick                                   0x20000fa8   Data           4  stm32f4xx_hal.o(.bss.uwTick)
    __heap_base                              0x20000fb0   Data           0  startup_stm32f401xc.o(HEAP)
    __heap_limit                             0x200013b0   Data           0  startup_stm32f401xc.o(HEAP)
    __initial_sp                             0x20001bb0   Data           0  startup_stm32f401xc.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000195

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00008c58, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00008c10, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000194   Data   RO         1278    RESET               startup_stm32f401xc.o
    0x08000194   0x08000194   0x00000000   Code   RO         1285  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000194   0x08000194   0x00000004   Code   RO         1326    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x08000198   0x08000198   0x00000004   Code   RO         1329    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         1331    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         1333    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x0800019c   0x0800019c   0x00000008   Code   RO         1334    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO         1336    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO         1338    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001a4   0x080001a4   0x00000004   Code   RO         1327    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a8   0x080001a8   0x00000024   Code   RO         1279    .text               startup_stm32f401xc.o
    0x080001cc   0x080001cc   0x00000062   Code   RO         1288    .text               mc_w.l(uldiv.o)
    0x0800022e   0x0800022e   0x00000024   Code   RO         1290    .text               mc_w.l(memcpya.o)
    0x08000252   0x08000252   0x00000024   Code   RO         1292    .text               mc_w.l(memseta.o)
    0x08000276   0x08000276   0x0000001c   Code   RO         1324    .text               mc_w.l(calloc.o)
    0x08000292   0x08000292   0x0000001e   Code   RO         1340    .text               mc_w.l(llshl.o)
    0x080002b0   0x080002b0   0x00000020   Code   RO         1342    .text               mc_w.l(llushr.o)
    0x080002d0   0x080002d0   0x00000030   Code   RO         1346    .text               mc_w.l(init.o)
    0x08000300   0x08000300   0x0000006c   Code   RO           19    .text.BSP_LED_Init  stm32f401_discovery.o
    0x0800036c   0x0800036c   0x00000020   Code   RO           23    .text.BSP_LED_Off   stm32f401_discovery.o
    0x0800038c   0x0800038c   0x00000020   Code   RO           21    .text.BSP_LED_On    stm32f401_discovery.o
    0x080003ac   0x080003ac   0x00000002   Code   RO         1186    .text.BusFault_Handler  stm32f4xx_it.o
    0x080003ae   0x080003ae   0x00000002   PAD
    0x080003b0   0x080003b0   0x00000002   Code   RO         1192    .text.DebugMon_Handler  stm32f4xx_it.o
    0x080003b2   0x080003b2   0x00000002   PAD
    0x080003b4   0x080003b4   0x0000004c   Code   RO          176    .text.FATFS_LinkDriver  ff_gen_drv.o
    0x08000400   0x08000400   0x00000030   Code   RO          180    .text.FATFS_UnLinkDriver  ff_gen_drv.o
    0x08000430   0x08000430   0x00000028   Code   RO          511    .text.HAL_Delay     stm32f4xx_hal.o
    0x08000458   0x08000458   0x0000019e   Code   RO          232    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x080005f6   0x080005f6   0x00000002   PAD
    0x080005f8   0x080005f8   0x0000000a   Code   RO          238    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08000602   0x08000602   0x00000002   PAD
    0x08000604   0x08000604   0x0000000c   Code   RO          503    .text.HAL_GetTick   stm32f4xx_hal.o
    0x08000610   0x08000610   0x00000008   Code   RO         1113    .text.HAL_HCD_Connect_Callback  usbh_conf.o
    0x08000618   0x08000618   0x00000008   Code   RO         1115    .text.HAL_HCD_Disconnect_Callback  usbh_conf.o
    0x08000620   0x08000620   0x00000006   Code   RO          791    .text.HAL_HCD_GetCurrentFrame  stm32f4xx_hal_hcd.o
    0x08000626   0x08000626   0x00000002   PAD
    0x08000628   0x08000628   0x00000006   Code   RO          793    .text.HAL_HCD_GetCurrentSpeed  stm32f4xx_hal_hcd.o
    0x0800062e   0x0800062e   0x00000002   PAD
    0x08000630   0x08000630   0x0000000e   Code   RO          785    .text.HAL_HCD_HC_GetURBState  stm32f4xx_hal_hcd.o
    0x0800063e   0x0800063e   0x00000002   PAD
    0x08000640   0x08000640   0x0000000c   Code   RO          787    .text.HAL_HCD_HC_GetXferCount  stm32f4xx_hal_hcd.o
    0x0800064c   0x0800064c   0x00000026   Code   RO          753    .text.HAL_HCD_HC_Halt  stm32f4xx_hal_hcd.o
    0x08000672   0x08000672   0x00000002   PAD
    0x08000674   0x08000674   0x000000a6   Code   RO          749    .text.HAL_HCD_HC_Init  stm32f4xx_hal_hcd.o
    0x0800071a   0x0800071a   0x00000002   PAD
    0x0800071c   0x0800071c   0x00000002   Code   RO         1121    .text.HAL_HCD_HC_NotifyURBChange_Callback  usbh_conf.o
    0x0800071e   0x0800071e   0x00000002   PAD
    0x08000720   0x08000720   0x000000a2   Code   RO          759    .text.HAL_HCD_HC_SubmitRequest  stm32f4xx_hal_hcd.o
    0x080007c2   0x080007c2   0x00000002   PAD
    0x080007c4   0x080007c4   0x00000950   Code   RO          761    .text.HAL_HCD_IRQHandler  stm32f4xx_hal_hcd.o
    0x08001114   0x08001114   0x0000008a   Code   RO          745    .text.HAL_HCD_Init  stm32f4xx_hal_hcd.o
    0x0800119e   0x0800119e   0x00000002   PAD
    0x080011a0   0x080011a0   0x000000d2   Code   RO         1107    .text.HAL_HCD_MspInit  usbh_conf.o
    0x08001272   0x08001272   0x00000002   PAD
    0x08001274   0x08001274   0x00000008   Code   RO         1119    .text.HAL_HCD_PortDisabled_Callback  usbh_conf.o
    0x0800127c   0x0800127c   0x00000008   Code   RO         1117    .text.HAL_HCD_PortEnabled_Callback  usbh_conf.o
    0x08001284   0x08001284   0x00000006   Code   RO          781    .text.HAL_HCD_ResetPort  stm32f4xx_hal_hcd.o
    0x0800128a   0x0800128a   0x00000002   PAD
    0x0800128c   0x0800128c   0x00000008   Code   RO         1111    .text.HAL_HCD_SOF_Callback  usbh_conf.o
    0x08001294   0x08001294   0x0000002e   Code   RO          777    .text.HAL_HCD_Start  stm32f4xx_hal_hcd.o
    0x080012c2   0x080012c2   0x00000002   PAD
    0x080012c4   0x080012c4   0x00000026   Code   RO          779    .text.HAL_HCD_Stop  stm32f4xx_hal_hcd.o
    0x080012ea   0x080012ea   0x00000002   PAD
    0x080012ec   0x080012ec   0x0000001a   Code   RO          501    .text.HAL_IncTick   stm32f4xx_hal.o
    0x08001306   0x08001306   0x00000002   PAD
    0x08001308   0x08001308   0x00000036   Code   RO          491    .text.HAL_Init      stm32f4xx_hal.o
    0x0800133e   0x0800133e   0x00000002   PAD
    0x08001340   0x08001340   0x00000048   Code   RO          493    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08001388   0x08001388   0x00000002   Code   RO          495    .text.HAL_MspInit   stm32f4xx_hal.o
    0x0800138a   0x0800138a   0x00000002   PAD
    0x0800138c   0x0800138c   0x00000022   Code   RO          559    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x080013ae   0x080013ae   0x00000002   PAD
    0x080013b0   0x080013b0   0x00000056   Code   RO          557    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08001406   0x08001406   0x00000002   PAD
    0x08001408   0x08001408   0x00000020   Code   RO          555    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08001428   0x08001428   0x00000164   Code   RO          364    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x0800158c   0x0800158c   0x00000070   Code   RO          366    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080015fc   0x080015fc   0x000003ac   Code   RO          362    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080019a8   0x080019a8   0x0000002c   Code   RO          567    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080019d4   0x080019d4   0x00000002   Code   RO         1182    .text.HardFault_Handler  stm32f4xx_it.o
    0x080019d6   0x080019d6   0x00000002   PAD
    0x080019d8   0x080019d8   0x00000002   Code   RO         1184    .text.MemManage_Handler  stm32f4xx_it.o
    0x080019da   0x080019da   0x00000002   PAD
    0x080019dc   0x080019dc   0x00000002   Code   RO         1180    .text.NMI_Handler   stm32f4xx_it.o
    0x080019de   0x080019de   0x00000002   PAD
    0x080019e0   0x080019e0   0x0000000c   Code   RO         1198    .text.OTG_FS_IRQHandler  stm32f4xx_it.o
    0x080019ec   0x080019ec   0x00000002   Code   RO         1194    .text.PendSV_Handler  stm32f4xx_it.o
    0x080019ee   0x080019ee   0x00000002   PAD
    0x080019f0   0x080019f0   0x00000002   Code   RO         1190    .text.SVC_Handler   stm32f4xx_it.o
    0x080019f2   0x080019f2   0x00000002   PAD
    0x080019f4   0x080019f4   0x00000004   Code   RO         1196    .text.SysTick_Handler  stm32f4xx_it.o
    0x080019f8   0x080019f8   0x00000056   Code   RO            2    .text.SystemInit    system_stm32f4xx.o
    0x08001a4e   0x08001a4e   0x00000002   PAD
    0x08001a50   0x08001a50   0x000000d2   Code   RO          975    .text.USBH_AllocPipe  usbh_pipes.o
    0x08001b22   0x08001b22   0x00000002   PAD
    0x08001b24   0x08001b24   0x00000026   Code   RO         1039    .text.USBH_BulkReceiveData  usbh_ioreq.o
    0x08001b4a   0x08001b4a   0x00000002   PAD
    0x08001b4c   0x08001b4c   0x00000032   Code   RO         1037    .text.USBH_BulkSendData  usbh_ioreq.o
    0x08001b7e   0x08001b7e   0x00000002   PAD
    0x08001b80   0x08001b80   0x0000000a   Code   RO          973    .text.USBH_ClosePipe  usbh_pipes.o
    0x08001b8a   0x08001b8a   0x00000002   PAD
    0x08001b8c   0x08001b8c   0x0000001a   Code   RO         1075    .text.USBH_ClrFeature  usbh_ctlreq.o
    0x08001ba6   0x08001ba6   0x00000002   PAD
    0x08001ba8   0x08001ba8   0x00000026   Code   RO         1035    .text.USBH_CtlReceiveData  usbh_ioreq.o
    0x08001bce   0x08001bce   0x00000002   PAD
    0x08001bd0   0x08001bd0   0x000001e2   Code   RO         1065    .text.USBH_CtlReq   usbh_ctlreq.o
    0x08001db2   0x08001db2   0x00000002   PAD
    0x08001db4   0x08001db4   0x00000032   Code   RO         1033    .text.USBH_CtlSendData  usbh_ioreq.o
    0x08001de6   0x08001de6   0x00000002   PAD
    0x08001de8   0x08001de8   0x00000024   Code   RO         1031    .text.USBH_CtlSendSetup  usbh_ioreq.o
    0x08001e0c   0x08001e0c   0x00000004   Code   RO         1151    .text.USBH_Delay    usbh_conf.o
    0x08001e10   0x08001e10   0x000000fa   Code   RO          997    .text.USBH_FindInterface  usbh_core.o
    0x08001f0a   0x08001f0a   0x00000002   PAD
    0x08001f0c   0x08001f0c   0x00000018   Code   RO          977    .text.USBH_FreePipe  usbh_pipes.o
    0x08001f24   0x08001f24   0x00000290   Code   RO         1061    .text.USBH_Get_CfgDesc  usbh_ctlreq.o
    0x080021b4   0x080021b4   0x000000ba   Code   RO         1057    .text.USBH_Get_DevDesc  usbh_ctlreq.o
    0x0800226e   0x0800226e   0x00000002   PAD
    0x08002270   0x08002270   0x000000bc   Code   RO         1063    .text.USBH_Get_StringDesc  usbh_ctlreq.o
    0x0800232c   0x0800232c   0x00000076   Code   RO          987    .text.USBH_Init     usbh_core.o
    0x080023a2   0x080023a2   0x00000002   PAD
    0x080023a4   0x080023a4   0x0000000e   Code   RO         1139    .text.USBH_LL_ClosePipe  usbh_conf.o
    0x080023b2   0x080023b2   0x00000002   PAD
    0x080023b4   0x080023b4   0x00000016   Code   RO         1019    .text.USBH_LL_Connect  usbh_core.o
    0x080023ca   0x080023ca   0x00000002   PAD
    0x080023cc   0x080023cc   0x0000002e   Code   RO         1021    .text.USBH_LL_Disconnect  usbh_core.o
    0x080023fa   0x080023fa   0x00000002   PAD
    0x080023fc   0x080023fc   0x00000020   Code   RO         1145    .text.USBH_LL_DriverVBUS  usbh_conf.o
    0x0800241c   0x0800241c   0x00000008   Code   RO         1135    .text.USBH_LL_GetLastXferSize  usbh_conf.o
    0x08002424   0x08002424   0x00000014   Code   RO         1131    .text.USBH_LL_GetSpeed  usbh_conf.o
    0x08002438   0x08002438   0x00000020   Code   RO         1149    .text.USBH_LL_GetToggle  usbh_conf.o
    0x08002458   0x08002458   0x00000008   Code   RO         1143    .text.USBH_LL_GetURBState  usbh_conf.o
    0x08002460   0x08002460   0x0000001e   Code   RO         1013    .text.USBH_LL_IncTimer  usbh_core.o
    0x0800247e   0x0800247e   0x00000002   PAD
    0x08002480   0x08002480   0x00000046   Code   RO         1123    .text.USBH_LL_Init  usbh_conf.o
    0x080024c6   0x080024c6   0x00000002   PAD
    0x080024c8   0x080024c8   0x0000001e   Code   RO         1137    .text.USBH_LL_OpenPipe  usbh_conf.o
    0x080024e6   0x080024e6   0x00000002   PAD
    0x080024e8   0x080024e8   0x0000000e   Code   RO         1017    .text.USBH_LL_PortDisabled  usbh_core.o
    0x080024f6   0x080024f6   0x00000002   PAD
    0x080024f8   0x080024f8   0x00000008   Code   RO         1015    .text.USBH_LL_PortEnabled  usbh_core.o
    0x08002500   0x08002500   0x0000000e   Code   RO         1133    .text.USBH_LL_ResetPort  usbh_conf.o
    0x0800250e   0x0800250e   0x00000002   PAD
    0x08002510   0x08002510   0x00000006   Code   RO         1011    .text.USBH_LL_SetTimer  usbh_core.o
    0x08002516   0x08002516   0x00000002   PAD
    0x08002518   0x08002518   0x00000024   Code   RO         1147    .text.USBH_LL_SetToggle  usbh_conf.o
    0x0800253c   0x0800253c   0x0000000e   Code   RO         1127    .text.USBH_LL_Start  usbh_conf.o
    0x0800254a   0x0800254a   0x00000002   PAD
    0x0800254c   0x0800254c   0x0000000e   Code   RO         1129    .text.USBH_LL_Stop  usbh_conf.o
    0x0800255a   0x0800255a   0x00000002   PAD
    0x0800255c   0x0800255c   0x00000022   Code   RO         1141    .text.USBH_LL_SubmitURB  usbh_conf.o
    0x0800257e   0x0800257e   0x00000002   PAD
    0x08002580   0x08002580   0x00000028   Code   RO         1211    .text.USBH_MSC_BOT_Init  usbh_msc_bot.o
    0x080025a8   0x080025a8   0x00000236   Code   RO         1213    .text.USBH_MSC_BOT_Process  usbh_msc_bot.o
    0x080027de   0x080027de   0x00000002   PAD
    0x080027e0   0x080027e0   0x00000016   Code   RO         1209    .text.USBH_MSC_BOT_REQ_GetMaxLUN  usbh_msc_bot.o
    0x080027f6   0x080027f6   0x00000002   PAD
    0x080027f8   0x080027f8   0x00000078   Code   RO         1227    .text.USBH_MSC_ClassRequest  usbh_msc.o
    0x08002870   0x08002870   0x00000058   Code   RO         1239    .text.USBH_MSC_GetLUNInfo  usbh_msc.o
    0x080028c8   0x080028c8   0x0000004e   Code   RO         1225    .text.USBH_MSC_InterfaceDeInit  usbh_msc.o
    0x08002916   0x08002916   0x00000002   PAD
    0x08002918   0x08002918   0x0000010e   Code   RO         1223    .text.USBH_MSC_InterfaceInit  usbh_msc.o
    0x08002a26   0x08002a26   0x00000002   PAD
    0x08002a28   0x08002a28   0x00000202   Code   RO         1229    .text.USBH_MSC_Process  usbh_msc.o
    0x08002c2a   0x08002c2a   0x00000002   PAD
    0x08002c2c   0x08002c2c   0x0000008e   Code   RO         1243    .text.USBH_MSC_RdWrProcess  usbh_msc.o
    0x08002cba   0x08002cba   0x00000002   PAD
    0x08002cbc   0x08002cbc   0x00000072   Code   RO         1241    .text.USBH_MSC_Read  usbh_msc.o
    0x08002d2e   0x08002d2e   0x00000002   PAD
    0x08002d30   0x08002d30   0x000000b0   Code   RO         1261    .text.USBH_MSC_SCSI_Inquiry  usbh_msc_scsi.o
    0x08002de0   0x08002de0   0x0000008c   Code   RO         1267    .text.USBH_MSC_SCSI_Read  usbh_msc_scsi.o
    0x08002e6c   0x08002e6c   0x00000070   Code   RO         1259    .text.USBH_MSC_SCSI_ReadCapacity  usbh_msc_scsi.o
    0x08002edc   0x08002edc   0x0000008c   Code   RO         1263    .text.USBH_MSC_SCSI_RequestSense  usbh_msc_scsi.o
    0x08002f68   0x08002f68   0x00000048   Code   RO         1257    .text.USBH_MSC_SCSI_TestUnitReady  usbh_msc_scsi.o
    0x08002fb0   0x08002fb0   0x0000008c   Code   RO         1265    .text.USBH_MSC_SCSI_Write  usbh_msc_scsi.o
    0x0800303c   0x0800303c   0x00000004   Code   RO         1231    .text.USBH_MSC_SOFProcess  usbh_msc.o
    0x08003040   0x08003040   0x00000026   Code   RO         1237    .text.USBH_MSC_UnitIsReady  usbh_msc.o
    0x08003066   0x08003066   0x00000002   PAD
    0x08003068   0x08003068   0x00000072   Code   RO         1245    .text.USBH_MSC_Write  usbh_msc.o
    0x080030da   0x080030da   0x00000002   PAD
    0x080030dc   0x080030dc   0x0000001a   Code   RO          971    .text.USBH_OpenPipe  usbh_pipes.o
    0x080030f6   0x080030f6   0x00000002   PAD
    0x080030f8   0x080030f8   0x000004ac   Code   RO         1009    .text.USBH_Process  usbh_core.o
    0x080035a4   0x080035a4   0x0000001a   Code   RO          991    .text.USBH_RegisterClass  usbh_core.o
    0x080035be   0x080035be   0x00000002   PAD
    0x080035c0   0x080035c0   0x00000012   Code   RO          993    .text.USBH_SelectInterface  usbh_core.o
    0x080035d2   0x080035d2   0x00000002   PAD
    0x080035d4   0x080035d4   0x0000001a   Code   RO         1067    .text.USBH_SetAddress  usbh_ctlreq.o
    0x080035ee   0x080035ee   0x00000002   PAD
    0x080035f0   0x080035f0   0x0000001a   Code   RO         1069    .text.USBH_SetCfg   usbh_ctlreq.o
    0x0800360a   0x0800360a   0x00000002   PAD
    0x0800360c   0x0800360c   0x0000001a   Code   RO         1073    .text.USBH_SetFeature  usbh_ctlreq.o
    0x08003626   0x08003626   0x00000002   PAD
    0x08003628   0x08003628   0x00000014   Code   RO         1001    .text.USBH_Start    usbh_core.o
    0x0800363c   0x0800363c   0x00000046   Code   RO         1164    .text.USBH_UserProcess  main.o
    0x08003682   0x08003682   0x00000002   PAD
    0x08003684   0x08003684   0x00000004   Code   RO         1087    .text.USBH_initialize  usbh_diskio_dma.o
    0x08003688   0x08003688   0x00000082   Code   RO         1095    .text.USBH_ioctl    usbh_diskio_dma.o
    0x0800370a   0x0800370a   0x00000002   PAD
    0x0800370c   0x0800370c   0x000000bc   Code   RO         1091    .text.USBH_read     usbh_diskio_dma.o
    0x080037c8   0x080037c8   0x00000018   Code   RO         1089    .text.USBH_status   usbh_diskio_dma.o
    0x080037e0   0x080037e0   0x000000cc   Code   RO         1093    .text.USBH_write    usbh_diskio_dma.o
    0x080038ac   0x080038ac   0x00000276   Code   RO          256    .text.USB_CoreInit  stm32f4xx_ll_usb.o
    0x08003b22   0x08003b22   0x00000002   PAD
    0x08003b24   0x08003b24   0x0000000e   Code   RO          262    .text.USB_DisableGlobalInt  stm32f4xx_ll_usb.o
    0x08003b32   0x08003b32   0x00000002   PAD
    0x08003b34   0x08003b34   0x0000004c   Code   RO          330    .text.USB_DriveVbus  stm32f4xx_ll_usb.o
    0x08003b80   0x08003b80   0x0000000e   Code   RO          260    .text.USB_EnableGlobalInt  stm32f4xx_ll_usb.o
    0x08003b8e   0x08003b8e   0x00000002   PAD
    0x08003b90   0x08003b90   0x000000be   Code   RO          274    .text.USB_FlushRxFifo  stm32f4xx_ll_usb.o
    0x08003c4e   0x08003c4e   0x00000002   PAD
    0x08003c50   0x08003c50   0x000000be   Code   RO          272    .text.USB_FlushTxFifo  stm32f4xx_ll_usb.o
    0x08003d0e   0x08003d0e   0x00000002   PAD
    0x08003d10   0x08003d10   0x00000008   Code   RO          334    .text.USB_GetCurrentFrame  stm32f4xx_ll_usb.o
    0x08003d18   0x08003d18   0x00000016   Code   RO          332    .text.USB_GetHostSpeed  stm32f4xx_ll_usb.o
    0x08003d2e   0x08003d2e   0x00000002   PAD
    0x08003d30   0x08003d30   0x00000008   Code   RO          266    .text.USB_GetMode   stm32f4xx_ll_usb.o
    0x08003d38   0x08003d38   0x00000182   Code   RO          342    .text.USB_HC_Halt   stm32f4xx_ll_usb.o
    0x08003eba   0x08003eba   0x00000002   PAD
    0x08003ebc   0x08003ebc   0x0000010a   Code   RO          336    .text.USB_HC_Init   stm32f4xx_ll_usb.o
    0x08003fc6   0x08003fc6   0x00000002   PAD
    0x08003fc8   0x08003fc8   0x00000008   Code   RO          340    .text.USB_HC_ReadInterrupt  stm32f4xx_ll_usb.o
    0x08003fd0   0x08003fd0   0x00000294   Code   RO          338    .text.USB_HC_StartXfer  stm32f4xx_ll_usb.o
    0x08004264   0x08004264   0x000002b2   Code   RO          324    .text.USB_HostInit  stm32f4xx_ll_usb.o
    0x08004516   0x08004516   0x00000002   PAD
    0x08004518   0x08004518   0x00000040   Code   RO          326    .text.USB_InitFSLSPClkSel  stm32f4xx_ll_usb.o
    0x08004558   0x08004558   0x00000010   Code   RO          308    .text.USB_ReadChInterrupts  stm32f4xx_ll_usb.o
    0x08004568   0x08004568   0x00000008   Code   RO          306    .text.USB_ReadInterrupts  stm32f4xx_ll_usb.o
    0x08004570   0x08004570   0x000000aa   Code   RO          292    .text.USB_ReadPacket  stm32f4xx_ll_usb.o
    0x0800461a   0x0800461a   0x00000002   PAD
    0x0800461c   0x0800461c   0x0000003e   Code   RO          328    .text.USB_ResetPort  stm32f4xx_ll_usb.o
    0x0800465a   0x0800465a   0x00000002   PAD
    0x0800465c   0x0800465c   0x00000368   Code   RO          264    .text.USB_SetCurrentMode  stm32f4xx_ll_usb.o
    0x080049c4   0x080049c4   0x00000938   Code   RO          346    .text.USB_StopHost  stm32f4xx_ll_usb.o
    0x080052fc   0x080052fc   0x00000002   Code   RO         1188    .text.UsageFault_Handler  stm32f4xx_it.o
    0x080052fe   0x080052fe   0x00000002   PAD
    0x08005300   0x08005300   0x0000011e   Code   RO          116    .text.create_chain  ff.o
    0x0800541e   0x0800541e   0x00000002   PAD
    0x08005420   0x08005420   0x0000065c   Code   RO          158    .text.dir_find      ff.o
    0x08005a7c   0x08005a7c   0x0000015e   Code   RO          138    .text.dir_next      ff.o
    0x08005bda   0x08005bda   0x00000002   PAD
    0x08005bdc   0x08005bdc   0x00000868   Code   RO          102    .text.dir_register  ff.o
    0x08006444   0x08006444   0x000000a6   Code   RO          128    .text.dir_sdi       ff.o
    0x080064ea   0x080064ea   0x00000002   PAD
    0x080064ec   0x080064ec   0x00000032   Code   RO           76    .text.disk_initialize  diskio.o
    0x0800651e   0x0800651e   0x00000002   PAD
    0x08006520   0x08006520   0x00000016   Code   RO           82    .text.disk_ioctl    diskio.o
    0x08006536   0x08006536   0x00000002   PAD
    0x08006538   0x08006538   0x00000020   Code   RO           78    .text.disk_read     diskio.o
    0x08006558   0x08006558   0x00000016   Code   RO           74    .text.disk_status   diskio.o
    0x0800656e   0x0800656e   0x00000002   PAD
    0x08006570   0x08006570   0x00000020   Code   RO           80    .text.disk_write    diskio.o
    0x08006590   0x08006590   0x0000006c   Code   RO          122    .text.f_close       ff.o
    0x080065fc   0x080065fc   0x00000118   Code   RO           94    .text.f_mount       ff.o
    0x08006714   0x08006714   0x000003d8   Code   RO           98    .text.f_open        ff.o
    0x08006aec   0x08006aec   0x00000308   Code   RO          112    .text.f_read        ff.o
    0x08006df4   0x08006df4   0x000000c0   Code   RO          118    .text.f_sync        ff.o
    0x08006eb4   0x08006eb4   0x00000346   Code   RO          114    .text.f_write       ff.o
    0x080071fa   0x080071fa   0x00000002   PAD
    0x080071fc   0x080071fc   0x000001aa   Code   RO          204    .text.ff_convert    unicode.o
    0x080073a6   0x080073a6   0x00000002   PAD
    0x080073a8   0x080073a8   0x00000004   Code   RO          193    .text.ff_memalloc   syscall.o
    0x080073ac   0x080073ac   0x00000004   Code   RO          195    .text.ff_memfree    syscall.o
    0x080073b0   0x080073b0   0x000000d0   Code   RO          206    .text.ff_wtoupper   unicode.o
    0x08007480   0x08007480   0x0000053e   Code   RO           96    .text.find_volume   ff.o
    0x080079be   0x080079be   0x00000002   PAD
    0x080079c0   0x080079c0   0x00000750   Code   RO          100    .text.follow_path   ff.o
    0x08008110   0x08008110   0x000000c8   Code   RO          110    .text.get_fat       ff.o
    0x080081d8   0x080081d8   0x00000004   Code   RO           84    .text.get_fattime   diskio.o
    0x080081dc   0x080081dc   0x00000218   Code   RO         1162    .text.main          main.o
    0x080083f4   0x080083f4   0x0000007c   Code   RO          106    .text.move_window   ff.o
    0x08008470   0x08008470   0x00000118   Code   RO          162    .text.put_fat       ff.o
    0x08008588   0x08008588   0x000000cc   Code   RO          120    .text.sync_fs       ff.o
    0x08008654   0x08008654   0x0000005a   Code   RO          152    .text.sync_window   ff.o
    0x080086ae   0x080086ae   0x0000000e   Code   RO         1350    i.__scatterload_copy  mc_w.l(handlers.o)
    0x080086bc   0x080086bc   0x00000002   Code   RO         1351    i.__scatterload_null  mc_w.l(handlers.o)
    0x080086be   0x080086be   0x0000000e   Code   RO         1352    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080086cc   0x080086cc   0x00000050   Code   RO         1296    i.free              mc_w.l(malloc.o)
    0x0800871c   0x0800871c   0x0000006c   Code   RO         1297    i.malloc            mc_w.l(malloc.o)
    0x08008788   0x08008788   0x00000010   Data   RO            7    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x08008798   0x08008798   0x00000080   Data   RO          164    .rodata.ExCvt       ff.o
    0x08008818   0x08008818   0x00000008   Data   RO           58    .rodata.GPIO_PIN    stm32f401_discovery.o
    0x08008820   0x08008820   0x00000100   Data   RO          208    .rodata.Tbl         unicode.o
    0x08008920   0x08008920   0x00000014   Data   RO         1097    .rodata.USBH_Driver  usbh_diskio_dma.o
    0x08008934   0x08008934   0x0000000a   Data   RO         1169    .rodata.str1.1      main.o
    0x0800893e   0x0800893e   0x00000004   Data   RO         1247    .rodata.str1.1      usbh_msc.o
    0x08008942   0x08008942   0x000002ae   Data   RO          209    .rodata.str2.2      unicode.o
    0x08008bf0   0x08008bf0   0x00000020   Data   RO         1349    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08008c10, Size: 0x00001bb0, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08008c10   0x00000004   Data   RW         1344    .data               mc_w.l(mvars.o)
    0x20000004   0x08008c14   0x00000004   Data   RW         1345    .data               mc_w.l(mvars.o)
    0x20000008   0x08008c18   0x00000008   Data   RW          546    .data..L_MergedGlobals  stm32f4xx_hal.o
    0x20000010   0x08008c20   0x00000010   Data   RW           57    .data.GPIO_PORT     stm32f401_discovery.o
    0x20000020   0x08008c30   0x00000004   Data   RW            6    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000024   0x08008c34   0x00000020   Data   RW         1248    .data.USBH_msc      usbh_msc.o
    0x20000044   0x08008c54   0x00000004   PAD
    0x20000048        -       0x0000002c   Zero   RW          165    .bss..L_MergedGlobals  ff.o
    0x20000074        -       0x00000005   Zero   RW         1171    .bss..L_MergedGlobals  main.o
    0x20000079   0x08008c54   0x00000003   PAD
    0x2000007c        -       0x00000230   Zero   RW         1168    .bss.MyFile         main.o
    0x200002ac        -       0x00000234   Zero   RW         1167    .bss.USBDISKFatFs   main.o
    0x200004e0        -       0x00000010   Zero   RW          184    .bss.disk           ff_gen_drv.o
    0x200004f0        -       0x000004d8   Zero   RW         1166    .bss.hUSB_Host      main.o
    0x200009c8        -       0x000003e0   Zero   RW         1153    .bss.hhcd           usbh_conf.o
    0x20000da8        -       0x00000200   Zero   RW         1098    .bss.scratch        usbh_diskio_dma.o
    0x20000fa8        -       0x00000004   Zero   RW          545    .bss.uwTick         stm32f4xx_hal.o
    0x20000fac   0x08008c54   0x00000004   PAD
    0x20000fb0        -       0x00000400   Zero   RW         1277    HEAP                startup_stm32f401xc.o
    0x200013b0        -       0x00000800   Zero   RW         1276    STACK               startup_stm32f401xc.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       162          0          0          0          0       2011   diskio.o
     11872        108        128          0         44      84138   ff.o
       124          0          0          0         16       3319   ff_gen_drv.o
       606         36         10          0       2369       9889   main.o
        36          8        404          0       3072        836   startup_stm32f401xc.o
       172          6          8         16          0      14110   stm32f401_discovery.o
       206          0          0          8          4       6881   stm32f4xx_hal.o
       196          0          0          0          0      10286   stm32f4xx_hal_cortex.o
       424          0          0          0          0       5481   stm32f4xx_hal_gpio.o
      3016         38          0          0          0      15621   stm32f4xx_hal_hcd.o
      1408          4          0          0          0       7682   stm32f4xx_hal_rcc.o
        32          0          0          0          0       4167   stm32f4xx_it.o
      6714         26          0          0          0      37071   stm32f4xx_ll_usb.o
         8          0          0          0          0        913   syscall.o
        86          0         16          4          0       2886   system_stm32f4xx.o
       634         10        942          0          0       3093   unicode.o
       582          0          0          0        992      15857   usbh_conf.o
      1754         40          0          0          0      12402   usbh_core.o
      1616         32          0          0          0      14787   usbh_ctlreq.o
       550          6         20          0        512       8627   usbh_diskio_dma.o
       212          0          0          0          0       8195   usbh_ioreq.o
      1482         10          4         32          0      13920   usbh_msc.o
       628         12          0          0          0      11371   usbh_msc_bot.o
       780          0          0          0          0      11948   usbh_msc_scsi.o
       270          0          0          0          0       6810   usbh_pipes.o

    ----------------------------------------------------------------------
     33746        <USER>       <GROUP>         60       7020     312301   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       176          0          0          0         11          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        28          0          0          0          0         76   calloc.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
       188         20          0          0          0        160   malloc.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
         0          0          0          8          0          0   mvars.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
       546         <USER>          <GROUP>          8          0        708   Library Totals
         0          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       546         38          0          8          0        708   mc_w.l

    ----------------------------------------------------------------------
       546         <USER>          <GROUP>          8          0        708   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     34292        374       1564         68       7020     312441   Grand Totals
     34292        374       1564         68       7020     312441   ELF Image Totals
     34292        374       1564         68          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                35856 (  35.02kB)
    Total RW  Size (RW Data + ZI Data)              7088 (   6.92kB)
    Total ROM Size (Code + RO Data + RW Data)      35924 (  35.08kB)

==============================================================================

