--cpu=Cortex-M4.fp.sp
"stm32f401-disco\system_stm32f4xx.o"
"stm32f401-disco\stm32f401_discovery.o"
"stm32f401-disco\diskio.o"
"stm32f401-disco\ff.o"
"stm32f401-disco\ff_gen_drv.o"
"stm32f401-disco\syscall.o"
"stm32f401-disco\unicode.o"
"stm32f401-disco\stm32f4xx_hal_dma_ex.o"
"stm32f401-disco\stm32f4xx_hal_gpio.o"
"stm32f401-disco\stm32f4xx_ll_usb.o"
"stm32f401-disco\stm32f4xx_hal_rcc.o"
"stm32f401-disco\stm32f4xx_hal_flash.o"
"stm32f401-disco\stm32f4xx_hal_dma.o"
"stm32f401-disco\stm32f4xx_hal_flash_ex.o"
"stm32f401-disco\stm32f4xx_hal_dcmi.o"
"stm32f401-disco\stm32f4xx_hal.o"
"stm32f401-disco\stm32f4xx_hal_cortex.o"
"stm32f401-disco\stm32f4xx_hal_i2c.o"
"stm32f401-disco\stm32f4xx_hal_hcd.o"
"stm32f401-disco\stm32f4xx_hal_pwr.o"
"stm32f401-disco\stm32f4xx_hal_spi.o"
"stm32f401-disco\usbh_pipes.o"
"stm32f401-disco\usbh_core.o"
"stm32f401-disco\usbh_ioreq.o"
"stm32f401-disco\usbh_ctlreq.o"
"stm32f401-disco\usbh_diskio_dma.o"
"stm32f401-disco\usbh_conf.o"
"stm32f401-disco\main.o"
"stm32f401-disco\stm32f4xx_it.o"
"stm32f401-disco\usbh_msc_bot.o"
"stm32f401-disco\usbh_msc.o"
"stm32f401-disco\usbh_msc_scsi.o"
"stm32f401-disco\startup_stm32f401xc.o"
--library_type=microlib --strict --scatter "STM32F401-DISCO\STM32F401_DISCO.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "STM32F401_DISCO.map" -o STM32F401-DISCO\STM32F401_DISCO.axf